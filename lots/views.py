from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from base import viewsets
from base.mixins import (
    CreateModelMixin,
    DestroyModelMixin,
    ListModelMixin,
    PartialUpdateModelMixin,
    RetrieveModelMixin,
)
from base.permissions import HasService<PERSON><PERSON>, IsOwner
from lots.filters import LotFilter
from lots.models import Lot
from lots.serializers import (
    LotCreateSerializer,
    LotListSerializer,
    LotReorderSerializer,
    LotUpdateSerializer,
)
from lots.services.lot_moderate_service import LotModerateService
from lots.services.lot_service import LotService


class LotViewSet(
    ListModelMixin,
    RetrieveModelMixin,
    CreateModelMixin,
    PartialUpdateModelMixin,
    DestroyModelMixin,
    viewsets.GenericViewSet,
):
    """
    API endpoints for LOTs
    """

    request_action_serializer_classes = {
        "create": LotCreateSerializer,
        "reorder": LotReorderSerializer,
        "partial_update": LotUpdateSerializer,
    }
    response_action_serializer_classes = {
        "create": LotListSerializer,
        "list": LotListSerializer,
        "retrieve": LotListSerializer,
        "reorder": LotListSerializer,
        "partial_update": LotListSerializer,
    }
    action_permission_classes = {
        "create": (IsAuthenticated,),
        "list": (AllowAny,),
        "retrieve": (AllowAny,),
        "reorder": (IsAuthenticated,),
        "destroy": (IsOwner,),
        "partial_update": (IsOwner,),
        "remove_blur": (HasServiceToken,),
    }
    action_filterset_classes = {"list": LotFilter}
    queryset = Lot.objects.select_related("user", "user__profile")

    def perform_create(self, serializer) -> Lot:
        user = self.request.user
        site_url = self.request.build_absolute_uri("/")

        lot_service = LotService()

        return lot_service.create_lot(
            user=user, **serializer.validated_data, site_url=site_url
        )

    def perform_partial_update(self, instance, serializer) -> Lot:
        site_url = self.request.build_absolute_uri("/")

        lot_service = LotService()

        return lot_service.update_lot(
            lot=serializer.instance, **serializer.validated_data, site_url=site_url
        )

    @action(methods=["POST"], detail=False)
    def reorder(self, request, *args, **kwargs):
        """
        Reorder LOTs, send the list in the order which objects will be stored
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        lot_service = LotService()
        lot_service.reorder_lots(
            user=request.user, lots=request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(
            request.user.lots.all(), many=True
        )

        return Response(response_serializer.data)

    def perform_destroy(self, instance, serializer=None):
        lot_service = LotService()

        lot_service.delete_lot(lot=instance)

    @action(
        methods=["PATCH"],
        detail=True,
        url_path="remove-blur",
        url_name="remove-blur",
    )
    def remove_blur(self, request, *args, **kwargs):
        """
        Endpoint for the service bot which removes blur from the lot
        """
        lot = self.get_object()

        service = LotModerateService()
        service.remove_blur(lot=lot)

        return Response(status=200, data={"message": "Blur removed"})
