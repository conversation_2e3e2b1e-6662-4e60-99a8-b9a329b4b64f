import os
from unittest.mock import MagicMock, call

from django.core.files.storage import default_storage

from accounts.services.email_service import EmailService
from base.base_test_case import BaseTestCase
from base.factories import LotWishFactory, UserFactory
from base.services.nsfw import NSFWDetectService
from lots.services.lot_moderate_service import LotModerateService
from lots.services.lot_tg_bot_service import LotTelegramBotService


class LotModerateServiceTests(BaseTestCase):
    def setUp(self):
        """Set up test case with mocked dependencies."""
        self.mock_nsfw_service = MagicMock(spec=NSFWDetectService)
        self.mock_tg_bot_service = MagicMock(spec=LotTelegramBotService)
        self.mock_email_service = MagicMock(spec=EmailService)

        self.service = LotModerateService(
            nsfw_service=self.mock_nsfw_service,
            tg_bot_service=self.mock_tg_bot_service,
            email_service=self.mock_email_service,
        )
        self.service.storage.location = "/var/www/media"

        self.user = UserFactory()
        self.site_url = "https://example.com"

    def test_fs_to_storage_path_with_valid_path(self):
        """Test _fs_to_storage_path with a valid filesystem path."""
        storage_location = default_storage.location
        test_path = os.path.join(storage_location, "images/test.jpg")

        result = self.service._fs_to_storage_path(test_path)

        self.assertEqual(result, "images/test.jpg")

    def test_fs_to_storage_path_with_invalid_path(self):
        """Test _fs_to_storage_path with a path not under storage location."""
        result = self.service._fs_to_storage_path("/some/random/path/test.jpg")

        self.assertIsNone(result)

    def test_moderate_lot_without_image(self):
        """Test moderating a lot that has no image."""
        lot = LotWishFactory(user=self.user, data={})

        result = self.service.moderate(str(lot.id), self.site_url)

        self.assertIn(f"Lot {lot.id} has no image_path", result)
        self.mock_nsfw_service.detect.assert_not_called()
        self.mock_tg_bot_service.send_lot_images_for_moderation.assert_not_called()
        self.mock_email_service.send_lot_blur_notification.assert_not_called()

    def test_moderate_lot_clean_image(self):
        """Test moderating a lot with a clean image (no nudity detected)."""
        lot = LotWishFactory(user=self.user, data={"image_path": "images/test.jpg"})

        self.mock_nsfw_service.detect.return_value = False

        result = self.service.moderate(str(lot.id), self.site_url)

        self.assertIn(f"Lot {lot.id} is clean", result)
        self.mock_nsfw_service.detect.assert_called_once()
        self.mock_nsfw_service.blur_image.assert_not_called()
        self.mock_tg_bot_service.send_lot_images_for_moderation.assert_not_called()
        self.mock_email_service.send_lot_blur_notification.assert_not_called()

    def test_moderate_lot_with_nudity(self):
        """Test moderating a lot where nudity is detected."""
        lot = LotWishFactory(
            user=self.user,
            data={
                "image_path": "images/test.jpg",
                "thumbnail_path": "images/test_thumb.jpg",
            },
        )

        self.mock_nsfw_service.detect.return_value = True
        self.mock_nsfw_service.blur_image.side_effect = [
            "/var/www/media/images/test_blurred.jpg",
            "/var/www/media/images/test_thumb_blurred.jpg",
        ]

        result = self.service.moderate(str(lot.id), self.site_url)

        self.assertIn(f"Lot {lot.id} contains nudity", result)
        self.assertIn("images blurred", result)

        lot.refresh_from_db()
        self.assertEqual(lot.data["blur_image_path"], "images/test_blurred.jpg")
        self.assertEqual(
            lot.data["blur_thumbnail_path"], "images/test_thumb_blurred.jpg"
        )

        self.mock_nsfw_service.detect.assert_called_once()
        self.assertEqual(self.mock_nsfw_service.blur_image.call_count, 2)
        self.mock_tg_bot_service.send_lot_images_for_moderation.assert_called_once_with(
            lot, self.site_url
        )
        self.mock_email_service.send_lot_blur_notification.assert_called_once_with(
            lot, self.site_url
        )

    def test_moderate_lot_with_nudity_no_thumbnail(self):
        """Test moderating a lot with nudity but no thumbnail."""
        lot = LotWishFactory(user=self.user, data={"image_path": "images/test.jpg"})

        self.mock_nsfw_service.detect.return_value = True
        self.mock_nsfw_service.blur_image.return_value = (
            "/var/www/media/images/test_blurred.jpg"
        )

        result = self.service.moderate(str(lot.id), self.site_url)

        self.assertIn(f"Lot {lot.id} contains nudity", result)

        lot.refresh_from_db()
        self.assertEqual(lot.data["blur_image_path"], "images/test_blurred.jpg")
        self.assertNotIn("blur_thumbnail_path", lot.data)

        self.mock_nsfw_service.blur_image.assert_called_once()
        self.mock_tg_bot_service.send_lot_images_for_moderation.assert_called_once()
        self.mock_email_service.send_lot_blur_notification.assert_called_once()

    def test_unblur_lot(self):
        """Test unblurring a lot."""
        lot = LotWishFactory(
            user=self.user,
            data={
                "image_path": "images/test.jpg",
                "thumbnail_path": "images/test_thumb.jpg",
                "blur_image_path": "images/test_blurred.jpg",
                "blur_thumbnail_path": "images/test_thumb_blurred.jpg",
            },
        )
        self.service.storage = MagicMock()
        self.service.remove_blur(lot)
        lot.refresh_from_db()

        self.assertNotIn("blur_image_path", lot.data)
        self.assertNotIn("blur_thumbnail_path", lot.data)

        self.service.storage.delete.assert_has_calls(
            [
                call("images/test_blurred.jpg"),
                call("images/test_thumb_blurred.jpg"),
            ]
        )
