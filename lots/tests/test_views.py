import json
from io import Bytes<PERSON>
from unittest import mock

from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test.utils import override_settings
from django.urls import reverse
from PIL import Image
from rest_framework import status

from accounts.models import Platform
from base.base_test_case import BaseTestCase
from lots.models import Lot
from payments.models import FeeService

User = get_user_model()


class TestLotViews(BaseTestCase):
    def setUp(self):
        self.user_data = {
            "username": "user",
            "email": "<EMAIL>",
            "password": "323jqnrgifsdg123",
        }
        self.user = User.objects.create_user(**self.user_data)
        self.wish_lot_with_empty_data = Lot.objects.create(
            user=self.user, data={}, type=Lot.Type.WISH, ordering=1
        )
        self.platform = Platform.objects.create(
            name="platform", icon=self.temporary_image()
        )

    @staticmethod
    def temporary_image():
        stream = BytesIO()
        image = Image.new("RGB", (100, 100))
        image.save(stream, format="jpeg")

        return SimpleUploadedFile(
            "file.jpg", stream.getvalue(), content_type="image/jpg"
        )

    def test_lot_list(self):
        response = self.client.get(reverse("v1:lots:lot-list"))
        self.assertEqual(
            response.status_code, status.HTTP_400_BAD_REQUEST
        )  # user filter required

        response = self.client.get(
            reverse("v1:lots:lot-list"), {"user": self.user.username}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)

        response = self.client.get(reverse("v1:lots:lot-list"), {"user": "not exists"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    @mock.patch("lots.tasks.moderate_lot.delay")
    def test_lot_create(self, mock_moderate_lot: mock.MagicMock):
        wish_data = {
            "lot_type": Lot.Type.WISH.value,
            "data[title]": "title",
            "data[price]": 100,
            "data[comment]": "comment",
            "data[image]": self.temporary_image(),
        }
        self.client.force_authenticate(user=self.user)
        response = self.client.post(
            reverse("v1:lots:lot-list"), data=wish_data, format="multipart"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        mock_moderate_lot.assert_called_with(
            response.json()["id"], "http://testserver/"
        )

        coupon_data = {
            "lot_type": Lot.Type.COUPON.value,
            "data[title]": "title",
            "data[price]": 100,
            "data[valid_due]": 360,
            "data[image]": self.temporary_image(),
            "data[platform]": str(self.platform.pk),
        }
        response = self.client.post(
            reverse("v1:lots:lot-list"), data=coupon_data, format="multipart"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        mock_moderate_lot.assert_called_with(
            response.json()["id"], "http://testserver/"
        )

        self.client.logout()
        response = self.client.post(
            reverse("v1:lots:lot-list"), data=wish_data, format="multipart"
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @override_settings(MAX_LOT_AMOUNT=500)
    @mock.patch("lots.tasks.moderate_lot.delay")
    def test_update_lot(self, mock_moderate_lot: mock.MagicMock):
        self.client.force_authenticate(user=self.user)

        FeeService.objects.filter().update(service_fee=5, payment_fee=5)

        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "lot_type": Lot.Type.WISH.value,
                "data[title]": "title",
                "data[price]": 100,
                "data[comment]": "comment",
                "data[image]": self.temporary_image(),
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.wish_lot_with_empty_data.refresh_from_db()
        self.assertEqual(self.wish_lot_with_empty_data.data["title"], "title")
        self.assertEqual(self.wish_lot_with_empty_data.data["price"], 100)
        self.assertEqual(self.wish_lot_with_empty_data.data["comment"], "comment")
        self.assertTrue(bool(self.wish_lot_with_empty_data.data["image_path"]))
        mock_moderate_lot.assert_called_once_with(
            response.json()["id"], "http://testserver/"
        )

        # test partial update without lot type
        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "data[title]": "title new here",
                "data[price]": 222,
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # test partial update with invalid lot type
        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "lot_type": Lot.Type.COUPON.value,
                "data[price]": 333,
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # test invalid price
        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "lot_type": Lot.Type.WISH.value,
                "data[title]": "title new here",
                "data[price]": 455,
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # test valid
        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "lot_type": Lot.Type.WISH.value,
                "data[title]": "title new here",
                "data[price]": 454,
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.wish_lot_with_empty_data.refresh_from_db()
        self.assertEqual(self.wish_lot_with_empty_data.data["title"], "title new here")
        self.assertEqual(self.wish_lot_with_empty_data.data["price"], 454)
        mock_moderate_lot.assert_called_once()

        self.client.logout()
        response = self.client.patch(
            reverse(
                "v1:lots:lot-detail", kwargs={"pk": self.wish_lot_with_empty_data.pk}
            ),
            data={
                "lot_type": Lot.Type.WISH.value,
                "data[title]": "title",
                "data[price]": 100,
                "data[comment]": "comment",
                "data[image]": self.temporary_image(),
            },
            format="multipart",
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_lot(self):
        image = self.temporary_image()
        thumbnail = self.temporary_image()

        lot = Lot.objects.create(
            user=self.user,
            data={
                "title": "title",
                "price": 100,
                "comment": "comment",
                "image_path": default_storage.save(image.name, image),
                "thumbnail_path": default_storage.save(thumbnail.name, thumbnail),
            },
            type=Lot.Type.WISH,
            ordering=2,
        )

        response = self.client.delete(
            reverse("v1:lots:lot-detail", kwargs={"pk": lot.pk})
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            reverse("v1:lots:lot-detail", kwargs={"pk": lot.pk})
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Lot.objects.filter(pk=lot.pk).exists())
        self.assertFalse(default_storage.exists(lot.data["image_path"]))
        self.assertFalse(default_storage.exists(lot.data["thumbnail_path"]))

    def test_lots_reorder(self):
        coupon_lot = Lot.objects.create(
            user=self.user, data={}, type=Lot.Type.COUPON, ordering=2
        )

        response = self.client.post(
            reverse("v1:lots:lot-reorder"),
            data=json.dumps(
                [str(self.wish_lot_with_empty_data.pk), str(coupon_lot.pk)]
            ),
            content_type="application/json",
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        self.client.force_authenticate(user=self.user)
        response = self.client.post(
            reverse("v1:lots:lot-reorder"),
            data=json.dumps(
                [str(self.wish_lot_with_empty_data.pk), str(coupon_lot.pk)]
            ),
            content_type="application/json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.wish_lot_with_empty_data.refresh_from_db()
        self.assertEqual(self.wish_lot_with_empty_data.ordering, 2)
        coupon_lot.refresh_from_db()
        self.assertEqual(coupon_lot.ordering, 1)
