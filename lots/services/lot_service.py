import logging
import uuid
from io import BytesIO

from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import transaction
from django.db.models import Max, Model
from django.utils.translation import gettext_lazy as _
from PIL import Image, ImageOps
from rest_framework import serializers

from base.mixins import ThumbnailMixin
from lots.models import Lot
from lots.tasks import moderate_lot

logger = logging.getLogger(__name__)

User = get_user_model()


class LotService(ThumbnailMixin):
    """
    Service for LOT model
    """

    IMAGE_QUALITY = 90
    THUMBNAIL_SIZE = (400, 510)
    THUMBNAIL_QUALITY = 90
    DEFAULT_DELETE_FILE_KEYS = [
        "image_path",
        "blur_image_path",
        "thumbnail_path",
        "blur_thumbnail_path",
    ]

    @staticmethod
    def _generate_image_name(extension: str = ".webp") -> tuple[str, str]:
        """
        Generate unique image name and thumbnail name

        Args:
            extension (str):

        Returns:
            tuple[str, str]: path to the image
        """
        name = f"{str(uuid.uuid4())}{extension}"

        return f"lots/{name}", f"lots/thumbnails/{name}"

    def _create_thumbnail(self, image: InMemoryUploadedFile) -> BytesIO:
        """
        Create thumbnail from image

        Args:
            image (InMemoryUploadedFile):

        Returns:
            BytesIO
        """

        with Image.open(image) as img:
            img = ImageOps.exif_transpose(img)

            img = self.make_thumbnail(img)

            buffer = BytesIO()
            img.save(buffer, format="WEBP", quality=self.THUMBNAIL_QUALITY)
            buffer.seek(0)

            return buffer

    def _convert_to_webp(self, image: InMemoryUploadedFile) -> BytesIO:
        """
        Convert image to webp format

        Args:
            image (InMemoryUploadedFile): image from user

        Returns:
            BytesIO: converted image
        """
        with Image.open(image) as img:
            img = ImageOps.exif_transpose(img)

            if img.mode != "RGB":
                img = img.convert("RGB")

            buffer = BytesIO()
            img.save(buffer, format="WEBP", quality=self.IMAGE_QUALITY)
            buffer.seek(0)

            return buffer

    def _create_image_with_thumbnail(
        self, image: InMemoryUploadedFile
    ) -> tuple[str, str]:
        """
        Create image with thumbnail

        Args:
            image (InMemoryUploadedFile): image from user

        Returns:
            tuple[str, str]: image and thumbnail paths
        """
        webp_image = self._convert_to_webp(image)
        thumbnail = self._create_thumbnail(image)

        image_name, thumbnail_name = self._generate_image_name()

        path_to_image = default_storage.save(image_name, webp_image)
        path_to_thumbnail = default_storage.save(thumbnail_name, thumbnail)

        return path_to_image, path_to_thumbnail

    def _prepare_lot_data(self, data: dict) -> None:
        if image := data.pop("image", None):
            path_to_image, path_to_thumbnail = self._create_image_with_thumbnail(image)
            data["image_path"] = path_to_image
            data["thumbnail_path"] = path_to_thumbnail

        for key, value in data.items():
            if isinstance(value, Model):
                data[key] = str(value.pk)

    @staticmethod
    def generate_new_lot_ordering(user: User) -> int:
        max_ordering = user.lots.aggregate(Max("ordering"))["ordering__max"]
        return (max_ordering or 0) + 1

    def create_lot(
        self, user: User, lot_type: Lot.Type, data: dict, site_url: str
    ) -> Lot:
        """
        Create new lot. If image in the data, it will be converted to the webp format
        and stored in the default storage

        Args:
            user (User): user from the request
            lot_type (Lot.Type): type of the lot
            data(dict):
            site_url(str): site url for moderation

        Returns:
            Lot: new lot
        """
        self._prepare_lot_data(data)

        ordering = self.generate_new_lot_ordering(user)

        new_lot = Lot.objects.create(
            type=lot_type, data=data, user=user, ordering=ordering
        )

        moderate_lot.delay(str(new_lot.id), site_url)

        return new_lot

    def _lot_ordering_generator(self, value: int):
        while value:
            yield value
            value -= 1

    def reorder_lots(self, user: User, lots: list[Lot]) -> None:
        """
        Reorder lots in order they are sent in the list

        Args:
            user (User): user from the request
            lots (list[Lot]): list of lots

        Returns:
            None
        """
        ordering_generator = self._lot_ordering_generator(len(lots))

        with transaction.atomic():
            user.lots.update(ordering=None)

            for lot in lots:
                lot.ordering = next(ordering_generator)

            Lot.objects.bulk_update(lots, ["ordering"])

    def update_lot(
        self, lot: Lot, lot_type: Lot.Type, data: dict, site_url: str
    ) -> Lot:
        """
        Update lot. If image in the data, it will be converted to the webp format
        and stored in the default storage

        Args:
            lot (Lot): Lot from db
            lot_type (Lot.Type):
            data(dict):
            site_url(str): site url for moderation

        Returns:
            Lot: updated lot

        """
        if lot.type != lot_type:
            raise serializers.ValidationError({"lot_type": _("Invalid lot type")})

        self._prepare_lot_data(data)

        if new_image := data.get("image_path"):
            try:
                self._delete_images_from_storage(lot)
            except Exception as e:
                logger.error(
                    f"Error deleting images from storage: {e} during updating lot"
                )

        for key, value in data.items():
            lot.data[key] = value

        lot.save()

        if new_image:
            moderate_lot.delay(str(lot.id), site_url)

        return lot

    def _delete_images_from_storage(
        self, lot: Lot, file_keys: list[str] = None
    ) -> None:
        """
        Delete images from storage

        Args:
            lot (Lot): Lot from db
            file_keys (list[str]): list of keys to delete. If not provided, DEFAULT_DELETE_FILE_KEYS will be used

        Returns:
            None
        """
        delete_file_keys = file_keys or self.DEFAULT_DELETE_FILE_KEYS

        for key in delete_file_keys:
            if path := lot.data.pop(key, None):
                default_storage.delete(path)

    def delete_lot(self, lot: Lot) -> None:
        """
        Delete lot

        Args:
            lot (Lot): Lot from db

        Returns:
            None
        """

        lot.delete()
        self._delete_images_from_storage(lot)
