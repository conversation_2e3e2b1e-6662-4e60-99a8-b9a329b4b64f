import logging

import telebot
from django.conf import settings
from django.core.files.storage import default_storage

from base.services.base_tg_bot_service import BaseTelegramBotService
from lots.exceptions import SendLotForModerationException
from lots.models import Lot

logger = logging.getLogger(__name__)


class LotTelegramBotService(BaseTelegramBotService):
    def __init__(
        self, token: str = settings.SERVICE_TG_BOT_TOKEN, parse_mode: str = "HTML"
    ):
        super().__init__(token, parse_mode)
        self.chat_id = settings.SERVICE_CHAT_ID

    def _get_moderation_keyboard(self, lot_id: str):
        keyboard = telebot.types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            telebot.types.InlineKeyboardButton(
                text="Remove blur", callback_data=f"blur_remove_{lot_id}"
            )
        )
        keyboard.add(
            telebot.types.InlineKeyboardButton(
                text="Accept blur", callback_data=f"blur_accept_{lot_id}"
            )
        )
        return keyboard

    def _get_lot_image_url(self, lot: Lot, site_url: str) -> str:
        """
        Generate lot image url

        Args:
            lot (Lot): lot
            site_url (str): site url for url

        Returns:
            str: lot image url
        """
        return f"{site_url}{default_storage.url(lot.data['image_path'])}"

    def send_lot_images_for_moderation(self, lot: Lot, site_url: str):
        """
        Send lot images for moderation

        Args:
            lot (Lot): lot
            site_url (str): site url for url

        Returns:
            None
        """
        text = (
            f"Lot {lot.id} contains nudity, images blurred\n\nUser: {lot.user.username}"
        )
        image_url = self._get_lot_image_url(lot, site_url)

        try:
            self.bot.send_photo(
                chat_id=self.chat_id,
                photo=image_url,
                caption=text,
                reply_markup=self._get_moderation_keyboard(str(lot.id)),
            )
            logger.info(f"Lot {lot.id} sent for moderation")
        except telebot.apihelper.ApiException as err:
            logger.error(f"Error sending lot {lot.id} for moderation: {err}")

            raise SendLotForModerationException(
                "Error sending lot images for moderation"
            )
