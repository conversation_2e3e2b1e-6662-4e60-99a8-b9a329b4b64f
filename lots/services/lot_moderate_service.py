import logging
import os

from django.core.files.storage import default_storage
from django.db import transaction

from accounts.services.email_service import EmailService
from base.services.base_tg_bot_service import BaseTelegramBotService
from base.services.nsfw import NSFWDetectService
from lots.models import Lot
from lots.services.lot_tg_bot_service import LotTelegramBotService

logger = logging.getLogger(__name__)


class LotModerateService:
    """
    Service for moderating lots.
    """

    def __init__(
        self,
        nsfw_service: NSFWDetectService = None,
        tg_bot_service: BaseTelegramBotService = None,
        email_service: EmailService = None,
    ):
        self.nsfw_service = nsfw_service or NSFWDetectService()
        self.tg_bot_service = tg_bot_service or LotTelegramBotService()
        self.email_service = email_service or EmailService()
        self.storage = default_storage

    def _fs_to_storage_path(self, fs_path: str) -> str | None:
        """
        Given an absolute filesystem path (e.g. /var/www/media/…),
        return the storage-relative path (e.g. images/foo.jpg) by
        stripping off `storage.location`

        Args:
            fs_path (str): absolute filesystem path

        Returns:
            str: storage-relative path
        """
        storage_base = getattr(self.storage, "location", None)

        if storage_base and fs_path.startswith(storage_base):
            rel_path = os.path.relpath(fs_path, storage_base)

            return rel_path.replace(os.path.sep, "/")

    def moderate(self, lot_id: str, site_url: str) -> str:
        """
        1. Load the Lot
        2. If no image → no‐op
        3. Detect nudity → if true, blur image + thumbnail atomically
        4. Return status string

        Args:
            lot_id (str): lot id
            site_url (str); site url for tg bot

        Returns:
            str: status string
        """
        lot = Lot.objects.get(id=lot_id)

        image_path = lot.data.get("image_path")

        if not image_path:
            msg = f"[LotNSFW] Lot {lot_id} has no image_path → skipping"
            logger.info(msg)

            return msg

        fs_path = self.storage.path(image_path)
        contains_nudity = self.nsfw_service.detect(fs_path)

        if not contains_nudity:
            msg = f"[LotNSFW] Lot {lot_id} is clean"
            logger.debug(msg)

            return msg

        self.blur(lot)

        msg = f"[LotNSFW] Lot {lot_id} contains nudity; images blurred"
        logger.warning(msg)

        self.tg_bot_service.send_lot_images_for_moderation(lot, site_url)

        self.email_service.send_lot_blur_notification(lot, site_url)

        return msg

    def remove_blur(self, lot: Lot) -> None:
        """
        Remove blur from lot

        Args:
            lot (Lot): Lot from db

        Returns:
            None
        """
        for key in ["blur_image_path", "blur_thumbnail_path"]:
            if image_path := lot.data.get(key):
                self.storage.delete(image_path)
                lot.data.pop(key)

        lot.save()

    def blur(self, lot: Lot) -> None:
        image_path = lot.data.get("image_path")

        if image_path:
            fs_path = self.storage.path(image_path)
            blurred_full = self.nsfw_service.blur_image(fs_path)

            with transaction.atomic():
                lot.data["blur_image_path"] = self._fs_to_storage_path(blurred_full)

                thumb = lot.data.get("thumbnail_path")
                if thumb:
                    thumb_fs = self.storage.path(thumb)
                    blurred_thumb = self.nsfw_service.blur_image(thumb_fs)
                    lot.data["blur_thumbnail_path"] = self._fs_to_storage_path(
                        blurred_thumb
                    )

                lot.save(update_fields=["data"])
