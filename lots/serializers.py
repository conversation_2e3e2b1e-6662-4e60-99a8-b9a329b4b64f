import math
from uuid import uuid4

from django.conf import settings
from django.core.files.storage import default_storage
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import OpenApiExample, extend_schema_serializer
from pillow_heif import HeifImagePlugin  # noqa
from rest_framework import serializers

from accounts.models import Platform
from accounts.serializers import PureUserSerializer
from lots.models import Lot
from payments.models import FeeService


class BaseLotDataSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=255)
    price = serializers.IntegerField(min_value=1)
    image = serializers.ImageField(
        error_messages={
            "required": _("Image is required"),
            "null": _("Image is required"),
            "invalid": _("Image is required"),
        },
        required=True,
        allow_empty_file=False,
        allow_null=False,
    )

    def validate_price(self, value):
        price = value

        # only one fee service currently
        fee_service = FeeService.objects.first()
        total_fee_percent = fee_service.service_fee + fee_service.payment_fee
        price_with_fee = math.ceil(price + price * total_fee_percent / 100)

        if price_with_fee > settings.MAX_LOT_AMOUNT:
            max_price_for_user = math.floor(
                settings.MAX_LOT_AMOUNT / (1 + total_fee_percent / 100)
            )

            raise serializers.ValidationError(
                _(f"Price can't be greater than {max_price_for_user}")
            )

        return value


class WishDataSerializer(BaseLotDataSerializer):
    comment = serializers.CharField(max_length=255, allow_blank=True)


class CouponDataSerializer(BaseLotDataSerializer):
    valid_due = serializers.IntegerField(min_value=1, required=False, allow_null=True)
    platform = serializers.PrimaryKeyRelatedField(queryset=Platform.objects.all())

    def to_internal_value(self, data):
        if data.get("valid_due") == "":
            data["valid_due"] = None

        return super().to_internal_value(data)


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "Wish Lot Example",
            description="SEND DATA IN multipart/form-data !!!!!!!!!!!!!!!!!!!!!!!!!!!",
            value={
                "data[title]": "IPhone",
                "data[price]": 100,
                "data[comment]": "I very want such phone",
                "data[image]": "<file from user>",
                "lot_type": "wish",
            },
            request_only=True,
        ),
        OpenApiExample(
            "Coupon Lot Example",
            description="SEND DATA IN multipart/form-data !!!!!!!!!!!!!!!!!!!!!!!!!!!",
            value={
                "data[title]": "Text",
                "data[price]": 100,
                "data[valid_due]": 360,
                "data[image]": "<file from user>",
                "data[platform]": str(uuid4()),
                "lot_type": "coupon",
            },
            request_only=True,
        ),
    ]
)
class LotSerializer(serializers.Serializer):
    data = serializers.DictField()
    lot_type = serializers.ChoiceField(choices=Lot.Type.choices)

    data_serializers_map = {"coupon": CouponDataSerializer, "wish": WishDataSerializer}

    def to_internal_value(self, data: dict) -> dict:
        lot_type = data.get("lot_type")

        if not lot_type:
            raise serializers.ValidationError({"lot_type": _("Lot type is required")})

        cleared_data = {}
        nested_data = {}
        for key, value in data.items():
            if "data[" in key:
                nested_data[key.replace("data[", "").replace("]", "")] = value
            else:
                cleared_data[key] = value

        serializer_class = self.data_serializers_map.get(lot_type)
        if not serializer_class:
            raise serializers.ValidationError({"type": _("Invalid lot type")})

        nested_serializer = serializer_class(data=nested_data, partial=self.partial)
        nested_serializer.is_valid(raise_exception=True)
        validated_data = nested_serializer.validated_data

        cleared_data["data"] = validated_data

        return cleared_data


class LotCreateSerializer(LotSerializer):
    def validate(self, attrs: dict) -> dict:
        lots_limit_per_user = 100

        user = self.context["request"].user

        if user.lots.count() >= lots_limit_per_user:
            raise serializers.ValidationError(
                _(f"You can't create more than {lots_limit_per_user} lots")
            )

        return attrs


class LotUpdateSerializer(LotSerializer):
    pass


class LotListSerializer(serializers.ModelSerializer):
    data = serializers.SerializerMethodField()
    lot_type = serializers.CharField(source="type")
    user = PureUserSerializer(read_only=True)

    class Meta:
        model = Lot
        fields = ("id", "lot_type", "data", "user")
        read_only_fields = fields

    def get_data(self, obj: Lot) -> dict:
        data = obj.data
        request = self.context["request"]

        if image_path := data.pop("image_path", None):
            image_path = data.pop("blur_image_path", None) or image_path

            data["image"] = request.build_absolute_uri(default_storage.url(image_path))

        if thumbnail_path := data.pop("thumbnail_path", None):
            thumbnail_path = data.pop("blur_thumbnail_path", None) or thumbnail_path

            data["thumbnail"] = request.build_absolute_uri(
                default_storage.url(thumbnail_path)
            )

        return data


class LotReorderSerializer(serializers.ListSerializer):
    child = serializers.PrimaryKeyRelatedField(queryset=Lot.objects.all())

    def validate(self, attrs):
        user = self.context["request"].user

        if len(attrs) != user.lots.count():
            raise serializers.ValidationError(_("Invalid lots number"))

        if not all([lot.user == user for lot in attrs]):
            raise serializers.ValidationError(_("Invalid lots"))

        return attrs
