# Generated by Django 5.1.3 on 2024-12-18 18:17

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Lot",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("wish", "Wish"), ("coupon", "Coupon")],
                        default="wish",
                        max_length=20,
                    ),
                ),
                ("ordering", models.PositiveIntegerField(null=True)),
                ("data", models.J<PERSON><PERSON>ield()),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lots",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ("-ordering",),
                "constraints": [
                    models.UniqueConstraint(
                        fields=("user", "ordering"), name="unique_user_ordering"
                    )
                ],
            },
        ),
    ]
