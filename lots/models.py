from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Lot(TimeStampedUUIDModel):
    class Type(models.TextChoices):
        WISH = "wish", _("Wish")
        COUPON = "coupon", _("Coupon")

    type = models.CharField(max_length=20, choices=Type.choices, default=Type.WISH)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="lots"
    )
    ordering = models.PositiveIntegerField(null=True)
    data = models.JSONField()

    class Meta:
        ordering = ("-ordering",)
        constraints = [
            models.UniqueConstraint(
                fields=("user", "ordering"), name="unique_user_ordering"
            )
        ]

    def __str__(self):
        return f"{self.user.username} - {self.type}"
