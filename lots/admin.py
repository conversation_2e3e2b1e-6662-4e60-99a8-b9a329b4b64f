from django.contrib import admin
from django.shortcuts import get_object_or_404, redirect
from django.urls.base import reverse
from django.urls.conf import path
from django.utils.html import format_html

from lots.models import Lot
from lots.services.lot_moderate_service import LotModerateService


@admin.register(Lot)
class LotAdmin(admin.ModelAdmin):
    list_display = ("updated_at", "user", "type", "ordering")
    list_filter = ("type",)
    search_fields = ("user__username", "type", "user__email")
    ordering = ("-updated_at",)

    def render_change_form(self, request, context, *args, **kwargs):
        lot_id = context.get("original").id if context.get("original") else None
        if lot_id:
            url = reverse("admin:lot-blur-unblur", args=[lot_id])
            context["adminform"].form.fields["data"].help_text = format_html(
                '<a class="button" href="{}">🔄 Blur/Unblur</a>', url
            )
        return super().render_change_form(request, context, *args, **kwargs)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")

    def get_urls(self):
        custom_urls = [
            path(
                "blur-unblur/<uuid:pk>/",
                self.admin_site.admin_view(self.blur_unblur_view),
                name="lot-blur-unblur",
            ),
        ]
        return custom_urls + super().get_urls()

    def blur_unblur_view(self, request, pk):
        lot = get_object_or_404(Lot, pk=pk)
        service = LotModerateService()

        if lot.data.get("blur_image_path"):
            service.remove_blur(lot)
        else:
            service.blur(lot)

        self.message_user(request, "Blur toggled successfully.")

        return redirect(
            reverse(
                f"admin:{self.opts.app_label}_{self.opts.model_name}_change", args=[pk]
            )
        )
