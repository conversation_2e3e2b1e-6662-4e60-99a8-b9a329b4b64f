services:
  api:
    image: ${DOCKER_IMAGE}
    container_name: dev_api
    ports:
      - "127.0.0.1:8000:8000"
    command: gunicorn core.wsgi:application --bind 0.0.0.0:8000 --workers 1 --threads 3 --max-requests 1000 --max-requests-jitter 100 --timeout=60
    env_file:
      - .env
    volumes:
      - /var/www/media/:/app/media
      - /var/log/wish_backend/:/app/logs
      - /var/www/staticfiles/:/app/staticfiles
    depends_on:
      - db
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    restart: always

  ws:
    image: ${DOCKER_IMAGE}
    container_name: dev_ws
    ports:
      - "127.0.0.1:8001:8000"
    entrypoint: /bin/bash -c "sleep 10 && daphne -b 0.0.0.0 -p 8000 core.asgi:application"
    env_file:
      - .env
    volumes:
      - /var/www/media/:/app/media
      - /var/log/wish_backend/:/app/logs
      - /var/www/staticfiles/:/app/staticfiles:ro
    depends_on:
      - db
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    restart: always

  db:
    image: postgres:15
    container_name: dev_db
    volumes:
      - dev_db_data:/var/lib/postgresql/data
    env_file:
      - .env
    restart: always

  redis:
    image: redis:latest
    container_name: dev_redis
    command: redis-server --appendonly yes --replica-read-only no --requirepass ${REDIS_PASSWORD}
    hostname: redis
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - dev_redis_data:/data
    env_file:
      - .env
    restart: always

  celery-worker:
    image: ${DOCKER_IMAGE}
    container_name: dev_celery-worker
    entrypoint: "/bin/sh -c 'celery -A core.celery worker --loglevel=info'"
    volumes:
      - /var/www/media/:/app/media
    env_file:
      - .env
    depends_on:
    - redis
    restart: always

  celery-beat:
    image: ${DOCKER_IMAGE}
    container_name: dev_celery-beat
    entrypoint: "/bin/sh -c 'celery -A core.celery beat -l info'"
    env_file:
      - .env
    depends_on:
    - api
    - celery-worker
    - redis
    restart: always

  flower:
    image: mher/flower
    container_name: dev_flower
    command: celery flower --url_prefix=special/flower
    environment:
      - FLOWER_PORT=5555
    ports:
      - "127.0.0.1:5555:5555"
    env_file:
      - .env
    depends_on:
      - api
      - celery-worker
      - redis
      - celery-beat
    restart: always

  # ----------------------------
  # Monitoring Services
  # ----------------------------

  victoriametrics:
    image: victoriametrics/victoria-metrics:v1.116.0
    container_name: victoriametrics
    depends_on:
      - api
      - flower
      - node-exporter
    volumes:
      - victoriametrics_data:/victoria-metrics-data
    command:
      - "--storageDataPath=/victoria-metrics-data"
      - "--httpListenAddr=:8428"
      - "--retentionPeriod=5d"

  vmagent:
    image: victoriametrics/vmagent:v1.116.0
    container_name: vmagent
    depends_on:
      - victoriametrics
    volumes:
      - vmagent_data:/vmagentdata
      - ./metrics/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - "--promscrape.config=/etc/prometheus/prometheus.yml"
      - "--remoteWrite.url=http://victoriametrics:8428/api/v1/write"
      - "--promscrape.config.strictParse=false"

  grafana:
    image: grafana/grafana-oss
    container_name: dev_grafana
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      - victoriametrics
    environment:
      - GF_SECURITY_ADMIN_USER=$GRAFANA_USER
      - GF_SECURITY_ADMIN_PASSWORD=$GRAFANA_PASSWORD
      - GF_SERVER_ROOT_URL=https://$DEPLOY_DOMAIN/special/grafana/
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
      - GF_SERVER_DOMAIN=$DEPLOY_DOMAIN
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter
    container_name: dev_node-exporter
    volumes:
      - "/proc:/host/proc:ro"
      - "/sys:/host/sys:ro"
      - "/:/rootfs:ro"
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points'
      - '^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    container_name: dev_pgadmin
    restart: always
    ports:
      - "127.0.0.1:5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: $PGADMIN_DEFAULT_EMAIL
      PGADMIN_DEFAULT_PASSWORD: $PGADMIN_DEFAULT_PASSWORD
    volumes:
      - pgadmin_data:/var/lib/pgadmin


volumes:
  dev_db_data:
  dev_redis_data:
  grafana_data:
  pgadmin_data:
  victoriametrics_data:
  vmagent_data:
