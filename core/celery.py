import os

from celery import Celery
from celery.signals import worker_ready
from celery_singleton import clear_locks

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings.local")

app = Celery("celery_app")
app.config_from_object("django.conf:settings", namespace="CELERY")

app.autodiscover_tasks()


@worker_ready.connect
def unlock_all(**kwargs):
    """Clear all locks when worker starts."""
    clear_locks(app)
