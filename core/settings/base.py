import os
import sys
from datetime import timed<PERSON>ta
from pathlib import Path

from django.urls import reverse_lazy
from dotenv import load_dotenv

load_dotenv(override=True)

BASE_DIR = Path(__file__).resolve().parent.parent.parent

CORS_ALLOWED_ORIGIN_REGEXES = []
ALLOWED_HOSTS = ["api"]

DEPLOY_DOMAIN = os.environ.get("DEPLOY_DOMAIN")

if DEPLOY_DOMAIN:
    ALLOWED_HOSTS.extend(
        [DEPLOY_DOMAIN, f"www.{DEPLOY_DOMAIN}"]
    )
    CSRF_TRUSTED_ORIGINS = [
        f"https://{DEPLOY_DOMAIN}",
        f"https://www.{DEPLOY_DOMAIN}",
    ]
    CORS_ALLOWED_ORIGIN_REGEXES = [
        rf"^https://{DEPLOY_DOMAIN}:*([0-9]+)?$",
        rf"^https://www.{DEPLOY_DOMAIN}:*([0-9]+)?$",
    ]

INTERNAL_IPS = [
    "127.0.0.1",
]

SECRET_KEY = os.environ["DJANGO_SECRET_KEY"]

INTERNAL_API_TOKEN = os.environ["INTERNAL_API_TOKEN"]

DEBUG = False

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

INSTALLED_APPS = [
    "daphne",
    # built-in apps
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # third-party apps
    "rest_framework",
    "django_filters",
    "drf_spectacular",
    "rest_framework_simplejwt",
    "django_otp",
    "django_otp.plugins.otp_totp",
    "debug_toolbar",
    "corsheaders",
    "django_celery_beat",
    "django_prometheus",
    "admin_honeypot",
    # local apps
    "accounts.apps.AccountsConfig",
    "accounts.apps.OTPAdminConfig",
    "sales",
    "lots",
    "payments",
]

MIDDLEWARE = [
    "django_prometheus.middleware.PrometheusBeforeMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django_otp.middleware.OTPMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_prometheus.middleware.PrometheusAfterMiddleware",
]

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ["POSTGRES_DB"],
        "USER": os.environ["POSTGRES_USER"],
        "PASSWORD": os.environ["POSTGRES_PASSWORD"],
        "HOST": os.environ["POSTGRES_HOST"],
        "PORT": os.environ.get("POSTGRES_PORT", "5432"),
    }
}

ROOT_URLCONF = os.environ.get("ROOT_URLCONF", "core.urls")

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTHENTICATION_BACKENDS = ["base.auth_backend.EmailOrUsernameModelBackend"]

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

STATIC_URL = "static/"
STATICFILES_DIRS = [BASE_DIR / "static"]
STATIC_ROOT = BASE_DIR / "staticfiles"

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {"anon": "80/min", "user": "150/min"},
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
    "DEFAULT_SCHEMA_CLASS": "base.openapi.RequestResponseAutoSchema",
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_FILTER_BACKENDS": ("base.filter_backends.FilterBackend",),
    "EXCEPTION_HANDLER": "base.exception_handlers.errors_formatter_exception_handler",
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.NamespaceVersioning",
    "DEFAULT_VERSION": "v1",
    "COERCE_DECIMAL_TO_STRING": False,
}

SPECTACULAR_SETTINGS = {
    "TITLE": "API",
    "DESCRIPTION": "API",
    "VERSION": "1.0.0",
    "COMPONENT_SPLIT_PATCH": True,
    "COMPONENT_SPLIT_REQUEST": True,
    "SERVE_PUBLIC": False,
    "SERVE_AUTHENTICATION": ["rest_framework.authentication.SessionAuthentication"],
    "SERVE_PERMISSIONS": ["rest_framework.permissions.IsAdminUser"],
    "SCHEMA_PATH_PREFIX": "/api/v[0-9]",
    "SWAGGER_UI_SETTINGS": {
        "docExpansion": "list",
        "filter": True,
        "tagsSorter": "alpha",
    },
    "SERVE_INCLUDE_SCHEMA": False,
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=15),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=15),
    "ROTATE_REFRESH_TOKENS": True,
    "TOKEN_OBTAIN_SERIALIZER": "accounts.serializers.EmailTokenObtainPairSerializer",
    "UPDATE_LAST_LOGIN": True,
}

AUTH_USER_MODEL = "accounts.User"
LOGIN_URL = reverse_lazy("admin:login")

# OTP settings
OTP_TOTP_ISSUER = "website"
OTP_ADMIN_LOGIN = True

# email
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_HOST = os.getenv("EMAIL_HOST", "smtp.gmail.com")
EMAIL_HOST_USER = os.environ["EMAIL_HOST_USER"]
EMAIL_HOST_PASSWORD = os.environ["EMAIL_HOST_PASSWORD"]
EMAIL_PORT = 587

# ttl activation token
PASSWORD_RESET_TIMEOUT = 86400

# processing
PAYMENT_PROCESSING_SERVICE = os.environ["PAYMENT_PROCESSING_SERVICE"]
MAX_LOT_AMOUNT = int(os.environ["MAX_LOT_AMOUNT"])

# stripe
STRIPE_SECRET_KEY = os.environ["STRIPE_SECRET_KEY"]
STRIPE_CURRENCY = "usd"

# platega
PLATEGA_SECRET_KEY = os.environ["PLATEGA_SECRET_KEY"]
PLATEGA_BASE_URL = os.environ["PLATEGA_BASE_URL"]
PLATEGA_PAYMENT_METHOD = os.environ["PLATEGA_PAYMENT_METHOD"]
PLATEGA_MERCHANT_ID = os.environ["PLATEGA_MERCHANT_ID"]

# safon
SAFON_CLIENT_ID = os.environ["SAFON_CLIENT_ID"]
SAFON_CLIENT_SECRET = os.environ["SAFON_CLIENT_SECRET"]
SAFON_BASE_URL = os.environ["SAFON_BASE_URL"]
SAFON_CATEGORY_ID = os.environ["SAFON_CATEGORY_ID"]

# withdraw
WITHDRAW_MIN_AMOUNT = int(os.environ["WITHDRAW_MIN_AMOUNT"])

# celery
CELERY_BROKER_URL = os.environ["CELERY_BROKER_URL"]
CELERY_RESULT_BACKEND = os.environ["CELERY_RESULT_BACKEND"]
CELERY_ACCEPT_CONTENT = ["application/json", "application/x-python-serialize"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_TASK_ACKS_LATE = True
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_RESULT_EXPIRES = 60 * 60 * 24
CELERY_TASK_COMPRESSION = "gzip"
CELERY_RESULT_COMPRESSION = "gzip"
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

# cache
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": os.environ["DJANGO_CACHE_URL"],
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

# service tg bot
SERVICE_TG_BOT_TOKEN = os.environ["SERVICE_TG_BOT_TOKEN"]
SERVICE_CHAT_ID = os.environ["SERVICE_CHAT_ID"]

# payment notification tg bot
PAYMENT_NOTIFICATION_BOT_TOKEN = os.environ["PAYMENT_NOTIFICATION_BOT_TOKEN"]

# channels
ASGI_APPLICATION = "core.asgi.application"
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [os.environ["CHANNEL_LAYERS_URL"]],
            "serializer_format": "json",
        },
    },
}

if "test" in sys.argv or "test_coverage" in sys.argv:
    from .test import *  # noqa
