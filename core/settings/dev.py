import sentry_sdk

from .base import *

CORS_ALLOWED_ORIGIN_REGEXES.extend(
    [
        r"^http:\/\/localhost:*([0-9]+)?$",
        r"^https:\/\/localhost:*([0-9]+)?$",
        r"^http:\/\/127.0.0.1:*([0-9]+)?$",
        r"^https:\/\/127.0.0.1:*([0-9]+)?$",
    ]
)
ALLOWED_HOSTS.extend(["127.0.0.1", "localhost"])

DEBUG = True

MEDIA_ROOT = BASE_DIR / "media"
MEDIA_URL = "/media/"

OTP_ADMIN_LOGIN = False

DEBUG_TOOLBAR_CONFIG = {"UPDATE_ON_FETCH": True}

# sentry
sentry_sdk.init(
    dsn="https://<EMAIL>/4508720608641104",
    traces_sample_rate=1.0,
    _experiments={
        "continuous_profiling_auto_start": True,
    },
)
