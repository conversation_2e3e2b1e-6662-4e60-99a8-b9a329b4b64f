import sentry_sdk

from .base import *

if not os.environ.get("DEPLOY_DOMAIN"):
    raise ValueError("DEPLOY_DOMAIN environment variable is not set")

DEBUG = False

MEDIA_ROOT = BASE_DIR / "media"
MEDIA_URL = "/media/"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{name} {levelname} {asctime} {module} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "file": {
            "class": "logging.FileHandler",
            "filename": BASE_DIR / "logs" / "django.log",
            "formatter": "verbose",
            "level": "DEBUG",
        },
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "verbose",
            "level": "INFO",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file", "console"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}

OTP_ADMIN_LOGIN = os.environ.get("OTP_ADMIN_LOGIN") == "True"

# celery
CELERY_WORKER_CONCURRENCY = 2

# sentry
sentry_sdk.init(
    dsn="https://<EMAIL>/4508720659365968",
    traces_sample_rate=1.0,
    _experiments={
        "continuous_profiling_auto_start": True,
    },
)
