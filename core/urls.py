from debug_toolbar.toolbar import debug_toolbar_urls
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.urls import include, path
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

api_v1_patterns = [
    path("accounts/", include("accounts.urls", namespace="accounts")),
    path("lots/", include("lots.urls", namespace="lots")),
    path("payments/", include("payments.urls", namespace="payments")),
    path("sales/", include("sales.urls", namespace="sales")),
]


if settings.DEBUG:
    api_v1_docs = [
        path(
            "schema/",
            login_required(SpectacularAPIView.as_view(api_version="v1")),
            name="v1-schema",
        ),
        path(
            "docs/",
            login_required(SpectacularSwaggerView.as_view(url_name="v1-schema")),
            name="v1-swagger-ui",
        ),
    ]

    api_v1_patterns.extend(api_v1_docs)


urlpatterns = [
    # fake admin
    path("api/admin/", include("admin_honeypot.urls", namespace="admin_honeypot")),
    # monitoring
    path("", include("django_prometheus.urls")),
    # api v1
    path("api/v1/", include((api_v1_patterns, "v1"), namespace="v1")),
]

if settings.DEBUG:
    urlpatterns = (
        urlpatterns
        + [
            path("api/@dm1n/", admin.site.urls),
        ]
        + debug_toolbar_urls()
        + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
        + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    )
