# Generated by Django 5.1.3 on 2025-01-15 15:19

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("lots", "0001_initial"),
        ("payments", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Sale",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="The amount without fees.",
                        max_digits=10,
                    ),
                ),
                (
                    "lot_thumbnail",
                    models.ImageField(
                        help_text="The thumbnail of the lot.",
                        upload_to="sales/lots/thumbnails",
                    ),
                ),
                (
                    "lot_title",
                    models.CharField(help_text="The name of the lot.", max_length=255),
                ),
                (
                    "buyer",
                    models.ForeignKey(
                        help_text="The user who bought the lot from the seller.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchases",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "lot",
                    models.ForeignKey(
                        help_text="The lot that was sold.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sales",
                        to="lots.lot",
                    ),
                ),
                (
                    "payment",
                    models.OneToOneField(
                        help_text="The payment associated with the sale.",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="sale",
                        to="payments.payment",
                    ),
                ),
                (
                    "seller",
                    models.ForeignKey(
                        help_text="The user who sold the lot to the buyer.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="sales",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
    ]
