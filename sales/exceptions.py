class SaleServiceError(Exception):
    """
    Exception raised for errors related to the SaleService.
    """

    pass


class LotWithoutImageError(SaleServiceError):
    """
    Exception raised when a lot doesn't have an image.
    """

    pass


class LotWithoutPriceError(SaleServiceError):
    """
    Exception raised when a lot doesn't have a price.
    """

    pass


class LotWithoutTitleError(SaleServiceError):
    """
    Exception raised when a lot doesn't have a title.
    """

    pass


class PaymentNotCreatedError(SaleServiceError):
    """
    Exception raised when a payment is not created.
    """

    pass
