from rest_framework.permissions import IsAuthenticated

from base.exceptions import ServiceUnavailable
from base.mixins import CreateModelMixin, ListModelMixin
from base.pagination import HundredMaxLimitOffsetPagination
from base.viewsets import GenericViewSet
from payments.factories.payment_service_factory import get_payment_service
from sales.exceptions import SaleServiceError
from sales.models import Sale
from sales.serializers import (
    SaleCreateRequestSerializer,
    SaleCreateResponseSerializer,
    SaleListSerializer,
)
from sales.services.sale_service import SaleService


class SaleViewSet(ListModelMixin, CreateModelMixin, GenericViewSet):
    action_permission_classes = {
        "create": [IsAuthenticated],
        "list": [IsAuthenticated],
    }
    request_action_serializer_classes = {
        "create": SaleCreateRequestSerializer,
    }
    response_action_serializer_classes = {
        "create": SaleCreateResponseSerializer,
        "list": SaleListSerializer,
    }
    pagination_class = HundredMaxLimitOffsetPagination

    def get_queryset(self):
        return Sale.objects.filter(buyer=self.request.user).select_related(
            "payment", "seller"
        )

    def perform_create(self, serializer):
        user = self.request.user
        site_url = self.request.build_absolute_uri("/")

        payment_service = get_payment_service()
        sale_service = SaleService(payment_service=payment_service)

        try:
            sale = sale_service.create_sale(
                **serializer.validated_data, user=user, site_url=site_url
            )
        except SaleServiceError:
            raise ServiceUnavailable

        return sale
