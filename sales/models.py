from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Sale(TimeStampedUUIDModel):
    seller = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="sales",
        help_text=_("The user who sold the lot to the buyer."),
    )
    buyer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="purchases",
        help_text=_("The user who bought the lot from the seller."),
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_("The amount without fees.")
    )
    lot = models.ForeignKey(
        "lots.Lot",
        on_delete=models.SET_NULL,
        null=True,
        related_name="sales",
        help_text=_("The lot that was sold."),
    )
    payment = models.OneToOneField(
        "payments.Payment",
        on_delete=models.PROTECT,
        related_name="sale",
        help_text=_("The payment associated with the sale."),
        null=True,
    )
    lot_thumbnail = models.ImageField(
        upload_to="sales/lots/thumbnails",
        help_text=_("The thumbnail of the lot."),
    )
    lot_title = models.CharField(max_length=255, help_text=_("The name of the lot."))

    class Meta:
        ordering = ("-created_at",)

    def __str__(self) -> str:
        return f"{self.seller} -> {self.buyer} ({self.amount})"
