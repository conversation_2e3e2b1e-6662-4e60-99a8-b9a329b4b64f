from django.contrib import admin

from sales.models import Sale


@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = (
        "created_at",
        "seller",
        "buyer",
        "amount",
        "lot_title",
        "get_payment_status",
    )
    ordering = ("-created_at",)
    search_fields = ("seller__username", "buyer__username", "lot_title")

    def get_payment_status(self, obj):
        return obj.payment.status

    get_payment_status.short_description = "Payment Status"

    def get_queryset(self, request):
        return (
            super().get_queryset(request).select_related("payment", "buyer", "seller")
        )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
