from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers

from lots.models import Lot
from payments.models import Payment
from sales.models import Sale


class SaleCreateRequestSerializer(serializers.Serializer):
    lot = serializers.PrimaryKeyRelatedField(queryset=Lot.objects.all())


class SaleCreateResponseSerializer(serializers.ModelSerializer):
    session_id = serializers.CharField(source="payment.session_id")
    session_url = serializers.CharField(source="payment.session_url")

    class Meta:
        model = Sale
        fields = (
            "session_id",
            "session_url",
        )
        read_only_fields = fields


class SaleListSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()
    seller = serializers.CharField(source="seller.username")

    class Meta:
        model = Sale
        fields = (
            "id",
            "created_at",
            "amount",
            "lot_thumbnail",
            "lot_title",
            "status",
            "seller",
        )
        read_only_fields = fields

    @extend_schema_field(
        serializers.ChoiceField(
            choices=[
                "pending",
                "paid",
                "failed",
            ]
        )
    )
    def get_status(self, obj: Sale) -> str:
        match obj.payment.status:
            case Payment.StatusChoices.PENDING:
                return "pending"
            case Payment.StatusChoices.PAID:
                return "paid"
            case _:
                return "failed"
