import logging
from io import Bytes<PERSON>
from uuid import uuid4

from django.contrib.auth import get_user_model
from django.core.files.images import ImageFile
from django.core.files.storage import default_storage
from django.db import transaction
from PIL import Image

from base.mixins import ThumbnailMixin
from lots.models import Lot
from payments.exceptions.payment_service_exceptions import PaymentServiceError
from payments.services.payment_service import IPaymentService
from realtime.enums import EventTypeEnum
from realtime.services import broadcast_event
from sales.exceptions import (
    LotWithoutImageError,
    LotWithoutPriceError,
    LotWithoutTitleError,
    PaymentNotCreatedError,
)
from sales.models import Sale
from sales.serializers import SaleListSerializer

User = get_user_model()

logger = logging.getLogger(__name__)


class SaleService(ThumbnailMixin):
    THUMBNAIL_SIZE = (64, 64)
    THUMBNAIL_QUALITY = 60

    def __init__(self, payment_service: IPaymentService):
        self.payment_service = payment_service

    def _create_thumbnail(self, image_path: str) -> ImageFile:
        """
        Create thumbnail from image_path

        Args:
            image_path(str): path to image

        Returns:
            BytesIO: thumbnail
        """
        image_path = default_storage.path(image_path)

        with Image.open(image_path) as img:
            img = self.make_thumbnail(img)

            buffer = BytesIO()
            img.save(buffer, format="WEBP", quality=self.THUMBNAIL_QUALITY)
            buffer.seek(0)

            return ImageFile(buffer, name=f"{uuid4()}.webp")

    def create_sale(self, lot: Lot, user: User, site_url: str) -> Sale:
        """
        Create sale instance

        Args:
            lot(Lot): lot
            user(User): buyer
            site_url(str): current site url for callback
        """
        image_path = lot.data.get("image_path")
        if not image_path:
            logger.error(f"Lot: {lot.id} without image")

            raise LotWithoutImageError

        thumbnail = self._create_thumbnail(image_path)

        amount = lot.data.get("price")
        if not amount:
            logger.error(f"Lot: {lot.id} without price")

            raise LotWithoutPriceError

        title = lot.data.get("title")
        if not title:
            logger.error(f"Lot: {lot.id} without title")

            raise LotWithoutTitleError

        seller = lot.user
        buyer = user

        with transaction.atomic():
            sale = Sale.objects.create(
                seller=seller,
                buyer=buyer,
                amount=amount,
                lot=lot,
                lot_thumbnail=thumbnail,
                lot_title=lot.data.get("title"),
            )

            try:
                sale.payment = self.payment_service.create_payment(
                    sale=sale, site_url=site_url
                )
            except PaymentServiceError:
                logger.error(f"Payment for sale: {sale.id} not created")

                raise PaymentNotCreatedError("Payment not created")

            sale.save()

        broadcast_event(
            user_id=str(sale.buyer.id),
            event_type=EventTypeEnum.SALE_CREATED,
            payload=SaleListSerializer(sale).data,
        )

        return sale
