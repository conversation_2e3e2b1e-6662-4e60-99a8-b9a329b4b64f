from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Transaction(TimeStampedUUIDModel):
    """Model representing a transaction."""

    class TransactionType(models.TextChoices):
        SALE = "sale", _("Sale")
        WITHDRAW = "withdraw", _("Withdraw")

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        help_text=_("User associated with the transaction."),
        related_name="transactions",
    )
    type = models.CharField(
        max_length=20,
        choices=TransactionType.choices,
        help_text=_("Type of the transaction."),
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount of the transaction."),
    )

    def __str__(self) -> str:
        return f"{self.created_at}"
