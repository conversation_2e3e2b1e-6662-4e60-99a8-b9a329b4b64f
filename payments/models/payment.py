from decimal import Decimal

from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class Payment(TimeStampedUUIDModel):
    """Model representing a payment."""

    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        PAID = "paid", "Paid"
        EXPIRED = "expired", "Expired"
        CANCELED = "canceled", "Canceled"
        FAILED = "failed", "Failed"

    status = models.CharField(
        max_length=25,
        choices=StatusChoices.choices,
        default=StatusChoices.PENDING,
        db_index=True,
        help_text=_("Current status of the payment."),
    )
    session_url = models.URLField(
        max_length=511, help_text=_("URL for the payment session."), blank=True
    )
    session_id = models.CharField(
        max_length=255,
        help_text=_("Unique identifier of the payment session."),
        blank=True,
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Amount to be paid with service fee."),
    )
    fee_group = models.ForeignKey(
        "accounts.FeeGroup",
        on_delete=models.SET_NULL,
        null=True,
        help_text=_("Group fee associated with the payment."),
    )
    fee_service = models.ForeignKey(
        "payments.FeeService",
        on_delete=models.SET_NULL,
        null=True,
        help_text=_("Service fee associated with the payment."),
    )
    fee_group_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Amount of the group fee."),
    )
    fee_service_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Amount of the service fee."),
    )
    transaction = models.OneToOneField(
        "payments.Transaction",
        on_delete=models.PROTECT,
        help_text=_("Transaction associated with the payment."),
        related_name="payment",
        null=True,
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self) -> str:
        return f"{self.created_at.isoformat()}: {self.amount}"
