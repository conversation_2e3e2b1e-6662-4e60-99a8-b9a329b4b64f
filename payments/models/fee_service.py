from decimal import Decimal

from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class FeeService(TimeStampedUUIDModel):
    """Model representing fee service."""

    service_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0"))],
        help_text=_("Fee amount for the service."),
    )
    payment_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0"))],
        help_text=_("Fee amount for the payment."),
    )

    def __str__(self) -> str:
        return f"Service Fee: {self.service_fee}, Payment Fee: {self.payment_fee}"
