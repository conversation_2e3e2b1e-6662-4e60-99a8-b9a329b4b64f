# Generated by Django 5.1.3 on 2025-06-30 16:17

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0004_groupwithdraw_withdraw_group_withdraw_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="transaction",
            name="user",
            field=models.ForeignKey(
                help_text="User associated with the transaction.",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
