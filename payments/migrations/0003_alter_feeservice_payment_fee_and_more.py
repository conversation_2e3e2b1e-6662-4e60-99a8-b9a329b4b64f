# Generated by Django 5.1.3 on 2025-06-20 14:51

from decimal import Decimal

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0002_create_default_fee_service"),
    ]

    operations = [
        migrations.AlterField(
            model_name="feeservice",
            name="payment_fee",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Fee amount for the payment.",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0"))],
            ),
        ),
        migrations.AlterField(
            model_name="feeservice",
            name="service_fee",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Fee amount for the service.",
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(Decimal("0"))],
            ),
        ),
        migrations.AlterField(
            model_name="payment",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("paid", "Paid"),
                    ("expired", "Expired"),
                    ("canceled", "Canceled"),
                    ("failed", "Failed"),
                ],
                db_index=True,
                default="pending",
                help_text="Current status of the payment.",
                max_length=25,
            ),
        ),
    ]
