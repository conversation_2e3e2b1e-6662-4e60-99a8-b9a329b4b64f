# Generated by Django 5.1.3 on 2025-01-09 18:05

from django.db import migrations

# Generated by Django 5.1.3 on 2024-12-08 13:05


def create_default_fee_service(apps, schema_editor):
    service_fee = 10
    payment_fee = 5

    FeeService = apps.get_model("payments", "FeeService")

    FeeService.objects.get_or_create(service_fee=service_fee, payment_fee=payment_fee)


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(create_default_fee_service, migrations.RunPython.noop)
    ]
