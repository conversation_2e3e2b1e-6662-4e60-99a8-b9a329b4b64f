# Generated by Django 5.1.3 on 2025-01-15 15:19

import uuid
from decimal import Decimal

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0002_add_all_fee_group"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FeeService",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "service_fee",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Fee amount for the service.",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "payment_fee",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Fee amount for the payment.",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("sale", "Sale"), ("withdraw", "Withdraw")],
                        help_text="Type of the transaction.",
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the transaction.",
                        max_digits=10,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User associated with the transaction.",
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("paid", "Paid"),
                            ("expired", "Expired"),
                        ],
                        db_index=True,
                        default="pending",
                        help_text="Current status of the payment.",
                        max_length=25,
                    ),
                ),
                (
                    "session_url",
                    models.URLField(
                        blank=True,
                        help_text="URL for the payment session.",
                        max_length=511,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True,
                        help_text="Unique identifier of the payment session.",
                        max_length=255,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount to be paid with service fee.",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "fee_group_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the group fee.",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "fee_service_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the service fee.",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "fee_group",
                    models.ForeignKey(
                        help_text="Group fee associated with the payment.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="accounts.feegroup",
                    ),
                ),
                (
                    "fee_service",
                    models.ForeignKey(
                        help_text="Service fee associated with the payment.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="payments.feeservice",
                    ),
                ),
                (
                    "transaction",
                    models.OneToOneField(
                        help_text="Transaction associated with the payment.",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="payment",
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Withdraw",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the withdraw.",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("done", "Done"),
                            ("error", "Error"),
                        ],
                        db_index=True,
                        default="pending",
                        help_text="Status of the withdraw.",
                        max_length=25,
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        help_text="Crypto wallet address of the withdraw.",
                        max_length=255,
                    ),
                ),
                (
                    "transaction",
                    models.OneToOneField(
                        help_text="Transaction associated with the withdraw.",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="withdraw",
                        to="payments.transaction",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User associated with the withdraw.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="withdraws",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
