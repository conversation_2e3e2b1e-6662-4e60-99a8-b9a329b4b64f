# Generated by Django 5.1.3 on 2025-06-30 16:09

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0006_feegrouptgconfig"),
        ("payments", "0003_alter_feeservice_payment_fee_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="GroupWithdraw",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of the withdraw.",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("pending", "Pending"), ("done", "Done")],
                        db_index=True,
                        default="pending",
                        help_text="Status of the withdraw.",
                        max_length=25,
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        help_text="Crypto wallet address of the withdraw.",
                        max_length=255,
                    ),
                ),
                (
                    "fee_group",
                    models.ForeignKey(
                        help_text="Fee group associated with the withdraw.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="group_withdraws",
                        to="accounts.feegroup",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="withdraw",
            name="group_withdraw",
            field=models.ForeignKey(
                blank=True,
                help_text="Group withdraw associated with the withdraw.",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="withdraws",
                to="payments.groupwithdraw",
            ),
        ),
        migrations.AddConstraint(
            model_name="withdraw",
            constraint=models.UniqueConstraint(
                condition=models.Q(("status", "pending")),
                fields=("user",),
                name="unique_pending_withdraw_per_user",
            ),
        ),
        migrations.AddConstraint(
            model_name="groupwithdraw",
            constraint=models.UniqueConstraint(
                condition=models.Q(("status", "pending")),
                fields=("fee_group",),
                name="unique_pending_group_withdraw_per_fee_group",
            ),
        ),
    ]
