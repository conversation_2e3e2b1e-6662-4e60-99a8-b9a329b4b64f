import logging
from abc import ABC, abstractmethod

import requests

from payments.exceptions.crypto_service_exceptions import TronCryptoServiceError

logger = logging.getLogger(__name__)


class ICryptoService(ABC):
    @abstractmethod
    def validate_address(self, address: str) -> bool:
        """
        Validate address

        Args:
            address(str): address

        Returns:
            bool: True if address is valid
        """
        pass


class TronCryptoService(ICryptoService):
    URL = "https://api.shasta.trongrid.io/"

    def validate_address(self, address: str) -> bool:
        """
        Validate address

        Args:
            address(str): address

        Returns:
            bool: True if address is valid
        """
        url = f"{self.URL}wallet/validateaddress"

        try:
            response = requests.post(
                url,
                json={"address": address},
            )
            response.raise_for_status()

            return response.json().get("result", False)

        except requests.exceptions.RequestException:
            logger.error("Error validating address", exc_info=True)

            raise TronCryptoServiceError("Error validating address")
