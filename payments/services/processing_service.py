import logging
import math
from abc import ABC, abstractmethod
from decimal import Decimal
from urllib.parse import urljoin

import httpx
import stripe
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ImproperlyConfigured
from django.urls import reverse

from payments.exceptions.processing_exceptions import (
    PlategaCreateSessionError,
    PlategaRetrieveSessionError,
    SafonAuthError,
    SafonCreateSessionError,
    SafonRetrieveSessionError,
    StripeCreateSessionError,
    StripeRetrieveSessionError,
)
from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import Payment

logger = logging.getLogger(__name__)

User = get_user_model()


class IProcessingService(ABC):
    """Interface for processing services."""

    @abstractmethod
    def create_session(self, payment: Payment, site_url: str):
        """
        Create payment session

        Args:
            payment(Payment): payment instance
            site_url(str): url for callback
        """
        pass

    @abstractmethod
    def get_session_status(self, payment: Payment) -> Payment.StatusChoices:
        """
        Check session status and update payment status.

        Args:
            payment:

        Returns:
            Payment.StatusChoices: payment session status
        """
        pass


class StripeProcessingService(IProcessingService):
    """
    Service for handling Stripe payments integration.
    """

    def __init__(self):
        """
        Initialize Stripe service with API key from settings.
        """

        if not settings.STRIPE_SECRET_KEY:
            raise ImproperlyConfigured(
                {"error": "STRIPE_SECRET_KEY must be set in settings."}
            )

        stripe.api_key = settings.STRIPE_SECRET_KEY
        stripe.max_network_retries = 3

    @staticmethod
    def _amount_to_cents(amount: Decimal) -> int:
        """
        Convert amount to cents.

        Args:
            amount(Decimal): amount

        Returns:
            int: amount in cents
        """
        return int(amount * 100)

    def create_session(self, payment: Payment, site_url: str) -> tuple[str, str]:
        """
        Process payment using Stripe.

        Args:
            payment(Payment): payment instance
            site_url(str): site url for callback

        Returns:
            (session_id, session_url)
        """
        fee_service_amount_cents = self._amount_to_cents(payment.fee_service_amount)
        lot_amount_cents = (
            self._amount_to_cents(payment.amount) - fee_service_amount_cents
        )

        success_url = urljoin(site_url, "/orders")
        cancel_url = urljoin(site_url, f"/payment-cancel/{str(payment.id)}")

        try:
            session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[
                    {
                        "price_data": {
                            "currency": settings.STRIPE_CURRENCY,
                            "product_data": {"name": payment.sale.lot_title},
                            "unit_amount": lot_amount_cents,
                        },
                        "quantity": 1,
                    },
                    {
                        "price_data": {
                            "currency": settings.STRIPE_CURRENCY,
                            "product_data": {"name": "Service fee"},
                            "unit_amount": fee_service_amount_cents,
                        },
                        "quantity": 1,
                    },
                ],
                mode="payment",
                success_url=success_url,
                cancel_url=cancel_url,
                customer_email=payment.sale.buyer.email,
            )

            return session.id, session.url

        except stripe.error.StripeError as e:
            logger.error(e, exc_info=True)

            raise StripeCreateSessionError("Stripe error during session creating")

    def get_session_status(self, payment: Payment) -> Payment.StatusChoices:
        """
        Get current session status

        Args:
            payment(Payment): payment instance

        Returns:
            Payment.StatusChoices
        """
        try:
            if not payment.session_id:
                logger.error(f"Payment session_id is not set for payment: {payment.id}")

                raise PaymentWithoutSessionIdError

            session = stripe.checkout.Session.retrieve(payment.session_id)

            match session.status:
                case "complete":
                    return Payment.StatusChoices.PAID
                case "expired":
                    return Payment.StatusChoices.EXPIRED
                case _:
                    return Payment.StatusChoices.PENDING

        except stripe.error.StripeError as e:
            logger.error(e, exc_info=True)

            raise StripeRetrieveSessionError(
                "Stripe error during session status checking"
            )


class PlategaProcessingService(IProcessingService):
    """
    Service for handling Platega payments integration.
    """

    def __init__(self):
        """
        Initialize Platega service with keys from settings.
        """
        if (
            not settings.PLATEGA_SECRET_KEY
            or not settings.PLATEGA_BASE_URL
            or not settings.PLATEGA_PAYMENT_METHOD
            or not settings.PLATEGA_MERCHANT_ID
            or not settings.MAX_LOT_AMOUNT
        ):
            raise ImproperlyConfigured(
                {
                    "error": "PLATEGA_SECRET_KEY, PLATEGA_BASE_URL, PLATEGA_PAYMENT_METHOD, PLATEGA_MERCHANT_ID, MAX_LOT_AMOUNT must be set in settings."
                }
            )
        self.api_key = settings.PLATEGA_SECRET_KEY
        self.merchant_id = settings.PLATEGA_MERCHANT_ID
        self.base_url = settings.PLATEGA_BASE_URL
        self.payment_method = int(settings.PLATEGA_PAYMENT_METHOD)
        self.max_lot_amount = settings.MAX_LOT_AMOUNT

    def _get_headers(self) -> dict:
        return {
            "X-Secret": self.api_key,
            "X-MerchantId": self.merchant_id,
            "Content-Type": "application/json",
        }

    def create_session(self, payment: Payment, site_url: str) -> tuple[str, str]:
        """
        Process payment using platega.io

        Args:
            payment(Payment): payment instance
            site_url(str): site url for callback

        Returns:
            (session_id, session_url)
        """
        if payment.amount > settings.MAX_LOT_AMOUNT:
            raise PlategaCreateSessionError(
                f"Amount {payment.amount} is greater than max lot amount {settings.MAX_LOT_AMOUNT}"
            )

        return_url = urljoin(site_url, f"/list/{str(payment.sale.lot_id)}")
        failed_url = urljoin(site_url, f"/payment-cancel/{str(payment.id)}")

        try:
            data = {
                "paymentMethod": self.payment_method,
                "paymentDetails": {
                    "amount": float(payment.amount),
                    # ToDO EUR temporary
                    "currency": "EUR",
                },
                "description": payment.sale.lot_title,
                "return": return_url,
                "failedUrl": failed_url,
            }

            headers = self._get_headers()

            url = urljoin(self.base_url, "/transaction/process")

            response = httpx.post(
                url=url,
                json=data,
                headers=headers,
            )
            response.raise_for_status()

            return response.json()["transactionId"], response.json()["redirect"]

        except httpx.HTTPError as e:
            logger.error(e, exc_info=True)

            raise PlategaCreateSessionError("Platega error during session creating")

    def get_session_status(self, payment: Payment) -> Payment.StatusChoices:
        """
        Get current session status

        Args:
            payment(Payment): payment instance

        Returns:
            Payment.StatusChoices
        """
        try:
            if not payment.session_id:
                logger.error(f"Payment session_id is not set for payment: {payment.id}")

                raise PaymentWithoutSessionIdError

            url = urljoin(self.base_url, f"/transaction/{payment.session_id}")
            headers = self._get_headers()

            response = httpx.get(
                url=url,
                headers=headers,
            )
            response.raise_for_status()

            match response.json()["status"]:
                case "CONFIRMED":
                    return Payment.StatusChoices.PAID
                case "EXPIRED":
                    return Payment.StatusChoices.EXPIRED
                case "CANCELED":
                    return Payment.StatusChoices.CANCELED
                case "FAILED":
                    return Payment.StatusChoices.FAILED
                case _:
                    return Payment.StatusChoices.PENDING

        except httpx.HTTPError as e:
            logger.error(e, exc_info=True)

            raise PlategaRetrieveSessionError(
                "Platega error during session status checking"
            )


class SafonProcessingService(IProcessingService):
    """
    Service for handling Safon payments integration.
    """

    PRODUCT_NAME_PREFIX = "Giftlab service via"

    def __init__(self):
        """
        Initialize Safon service with keys from settings.
        """
        if (
            not settings.SAFON_CLIENT_SECRET
            or not settings.SAFON_BASE_URL
            or not settings.SAFON_CLIENT_ID
            or not settings.SAFON_CATEGORY_ID
            or not settings.MAX_LOT_AMOUNT
        ):
            raise ImproperlyConfigured(
                {
                    "error": "SAFON_CLIENT_ID, SAFON_CLIENT_SECRET, SAFON_BASE_URL, MAX_LOT_AMOUNT, SAFON_CATEGORY_ID must be set in settings."
                }
            )
        self.client_id = settings.SAFON_CLIENT_ID
        self.client_secret = settings.SAFON_CLIENT_SECRET
        self.base_url = settings.SAFON_BASE_URL
        self.category_id = settings.SAFON_CATEGORY_ID

    def _get_access_token(self) -> str:
        """
        Get access token from Safon based on client_id and client_secret.
        Checks token in cache before making request.

        Returns:
            str: access token
        """
        # Check if token is cached
        if cached_token := cache.get("safon_access_token"):
            return cached_token

        try:
            url = urljoin(self.base_url, "tokens")
            headers = self._get_headers()
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
            }

            response = httpx.post(
                url=url,
                headers=headers,
                json=data,
            )
            response.raise_for_status()

            token = response.json()["access_token"]
            expires_in = int(response.json()["expires_in"])

            # Cache token
            cache.set("safon_access_token", token, expires_in - 60)

            return token

        except httpx.HTTPError as e:
            logger.error(e, exc_info=True)

            raise SafonAuthError("Safon error during authentication")

    def _get_headers(self, with_auth: bool = False) -> dict:
        """
        Get headers for requests.

        Args:
            with_auth (bool): whether to include access token in headers

        Returns:
            dict: headers
        """
        headers = {
            "Content-Type": "application/json",
        }

        if with_auth:
            access_token = self._get_access_token()
            headers["Authorization"] = f"Bearer {access_token}"

        return headers

    @staticmethod
    def _session_url_english_localization(session_url: str) -> str:
        """
        Add /en/ to the url path after domain before other parameters

        Args:
            session_url:

        Returns:
            str: session url with /en/ in path

        """
        if "/en/" in session_url:
            return session_url

        return session_url.replace("/session/", "/en/session/")

    def _get_product_name(self, name: str) -> str:
        """
        Get product name for Safon.

        Args:
            name:

        Returns:
            str: product name
        """
        return f"{self.PRODUCT_NAME_PREFIX} {name}"

    def create_session(self, payment: Payment, site_url: str) -> tuple[str, str]:
        """
        Process payment using Safon

        Args:
            payment(Payment): payment instance
            site_url(str): site url for callback

        Returns:
            (session_id, session_url)
        """
        if payment.amount > settings.MAX_LOT_AMOUNT:
            raise SafonCreateSessionError(
                f"Amount {payment.amount} is greater than max lot amount {settings.MAX_LOT_AMOUNT}"
            )

        return_url = urljoin(site_url, "/orders")
        callback_url = urljoin(
            site_url, reverse("v1:payments:update-status", args=[payment.id])
        )
        cancel_url = urljoin(site_url, "/payment-cancel/cancel")

        product_name = self._get_product_name(payment.sale.lot_title)
        buyer = payment.sale.buyer

        try:
            data = {
                "email": buyer.email,
                "user_name": buyer.username,
                "product_name": product_name,
                "price": math.ceil(payment.amount),
                "currency": "USD",
                "category_id": self.category_id,
                "return_url": return_url,
                "callback_url": callback_url,
                "cancel_url": cancel_url,
            }

            headers = self._get_headers(with_auth=True)

            url = urljoin(self.base_url, "product/session")

            response = httpx.post(
                url=url,
                json=data,
                headers=headers,
            )
            response.raise_for_status()

            session_url = self._session_url_english_localization(
                response.json()["session_url"]
            )
            session_id = session_url.split("session/")[-1]

            return session_id, session_url

        except httpx.HTTPError as e:
            logger.error(e, exc_info=True)

            raise SafonCreateSessionError("Safon error during session creating")

    def get_session_status(self, payment: Payment) -> Payment.StatusChoices:
        """
        Get current session status

        Args:
            payment(Payment): payment instance

        Returns:
            Payment.StatusChoices
        """
        try:
            if not payment.session_id:
                logger.error(f"Payment session_id is not set for payment: {payment.id}")

                raise PaymentWithoutSessionIdError

            url = urljoin(self.base_url, f"product/session/{payment.session_id}")
            headers = self._get_headers(with_auth=True)

            response = httpx.get(
                url=url,
                headers=headers,
            )
            response.raise_for_status()

            match response.json()["status"]:
                case "paid":
                    return Payment.StatusChoices.PAID
                case "failed":
                    return Payment.StatusChoices.FAILED
                case "expired":
                    return Payment.StatusChoices.EXPIRED
                case _:
                    return Payment.StatusChoices.PENDING

        except httpx.HTTPError as e:
            logger.error(e, exc_info=True)

            raise SafonRetrieveSessionError(
                "Safon error during session status checking"
            )
