import logging
from decimal import Decimal

from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction

from accounts.models import FeeGroup
from payments.exceptions.crypto_service_exceptions import CryptoServiceError
from payments.exceptions.withdraw_service_exceptions import (
    WithdrawServiceCryptoServiceError,
    WithdrawServiceGroupBalanceError,
    WithdrawServiceInsufficientBalanceError,
    WithdrawServiceInvalidAddressError,
    WithdrawServicePendingGroupWithdrawError,
    WithdrawServicePendingWithdrawError,
)
from payments.models.withdraw import GroupWithdraw, Withdraw
from payments.services.crypto_service import ICryptoService, TronCryptoService
from payments.services.transaction_service import (
    ITransactionService,
    TransactionService,
)
from payments.services.wallet_service import IWalletService, WalletService

logger = logging.getLogger(__name__)


User = get_user_model()


class WithdrawService:
    """
    Service class for handling withdrawals, including address validation,
    balance checks, and transaction creation.
    """

    def __init__(
        self,
        crypto_service: ICryptoService | None = None,
        wallet_service: IWalletService | None = None,
        transaction_service: ITransactionService | None = None,
    ):
        self.crypto_service = crypto_service or TronCryptoService()
        self.wallet_service = wallet_service or WalletService()
        self.transaction_service = transaction_service or TransactionService()

    def create_withdraw(
        self,
        user: User,
        address: str,
    ) -> Withdraw:
        """
        Creates a withdrawal request for the given user.

        Args:
            user (User): The user instance requesting the withdrawal.
            address (str): The cryptocurrency address where the withdrawal will be sent.

        Raises:
            ValidationError: If the address is invalid, the balance is insufficient, or there is a pending withdrawal.
            ServiceUnavailable: If the crypto service is unavailable.

        Returns:
            Withdraw: The created Withdraw instance.
        """
        try:
            if not self.crypto_service.validate_address(address):
                raise WithdrawServiceInvalidAddressError("Invalid address")
        except CryptoServiceError:
            logger.error("Error validating address")

            raise WithdrawServiceCryptoServiceError("Crypto service is unavailable")

        with transaction.atomic():
            # Lock the user's row to prevent concurrent withdrawals
            user = User.objects.select_for_update().get(id=user.id)

            wallet_dto = self.wallet_service.get_wallet_dto(user=user)

            if wallet_dto.balance <= settings.WITHDRAW_MIN_AMOUNT:
                raise WithdrawServiceInsufficientBalanceError(
                    f"Current balance is not enough for the withdrawal. Min withdraw amount: {settings.WITHDRAW_MIN_AMOUNT}"
                )

            if (
                wallet_dto.last_withdraw
                and wallet_dto.last_withdraw.status == Withdraw.StatusChoices.PENDING
            ):
                raise WithdrawServicePendingWithdrawError(
                    "Already have a pending withdrawal."
                )

            withdraw = Withdraw.objects.create(
                user=user,
                amount=wallet_dto.balance,
                address=address,
            )

            withdraw.transaction = self.transaction_service.create_withdraw_transaction(
                withdraw=withdraw,
            )

            withdraw.save()

        return withdraw

    def create_group_withdraw(
        self, fee_group: FeeGroup, address: str, amount: Decimal
    ) -> GroupWithdraw:
        """
        Withdraw all funds from a fee group to a specified address.

        Args:
            fee_group (FeeGroup): The fee group from which funds will be withdrawn.
            address (str): The cryptocurrency address where the withdrawal will be sent.
            amount (Decimal): The amount to withdraw.

        Returns:
            GroupWithdraw: The created GroupWithdraw instance.
        """
        if fee_group.group_withdraws.filter(
            status=GroupWithdraw.StatusChoices.PENDING
        ).exists():
            raise WithdrawServicePendingGroupWithdrawError(
                "Already have a pending withdrawal."
            )

        try:
            if not self.crypto_service.validate_address(address):
                raise WithdrawServiceInvalidAddressError("Error validating address")
        except CryptoServiceError as e:
            logger.error(f"Error validating address: {e}", exc_info=True)

            raise WithdrawServiceCryptoServiceError("Crypto service is unavailable")

        with transaction.atomic():
            # Lock the user's rows to prevent concurrent withdrawals
            users = (
                User.objects.select_for_update()
                .filter(fee_group=fee_group)
                .prefetch_related("withdraws")
            )

            group_balance = self.wallet_service.get_group_balance(fee_group=fee_group)

            if group_balance <= settings.WITHDRAW_MIN_AMOUNT:
                raise WithdrawServiceInsufficientBalanceError(
                    f"Current balance is not enough for the withdrawal. Min withdraw amount: {settings.WITHDRAW_MIN_AMOUNT}"
                )

            if amount != group_balance:
                raise WithdrawServiceGroupBalanceError(
                    f"Group balance {group_balance} doesn't match requested amount {amount}"
                )

            group_withdraw = GroupWithdraw.objects.create(
                amount=amount,
                address=address,
                fee_group=fee_group,
                status=GroupWithdraw.StatusChoices.PENDING,
            )

            withdraws = []
            withdraw_amount = Decimal("0.00")
            for user in users:
                last_withdraw = self.wallet_service.get_last_user_withdraw(user=user)

                if (
                    last_withdraw
                    and last_withdraw.status == Withdraw.StatusChoices.PENDING
                ):
                    last_withdraw.address = address
                    last_withdraw.group_withdraw = group_withdraw
                    last_withdraw.save(update_fields=["address", "group_withdraw"])

                    withdraws.append(last_withdraw)
                    withdraw_amount += last_withdraw.amount
                else:
                    user_balance = self.wallet_service.get_user_balance(user=user)

                    if user_balance == Decimal("0.00"):
                        continue

                    new_withdraw = Withdraw.objects.create(
                        user=user,
                        amount=user_balance,
                        address=address,
                        status=Withdraw.StatusChoices.PENDING,
                        group_withdraw=group_withdraw,
                    )
                    new_withdraw.transaction = (
                        self.transaction_service.create_withdraw_transaction(
                            withdraw=new_withdraw,
                        )
                    )
                    new_withdraw.save(update_fields=["transaction"])

                    withdraws.append(new_withdraw)
                    withdraw_amount += user_balance

            if group_balance != withdraw_amount:
                logger.error(
                    f"Group balance {group_balance} doesn't match sum of withdraws {withdraw_amount} for group {fee_group.id}"
                )

                raise WithdrawServiceGroupBalanceError(
                    "Group balance doesn't match sum of withdraws"
                )

        return group_withdraw

    def mark_group_withdraw_as_done(self, group_withdraw: GroupWithdraw):
        with transaction.atomic():
            group_withdraw.status = GroupWithdraw.StatusChoices.DONE
            group_withdraw.save(update_fields=["status"])
            group_withdraw.withdraws.update(status=Withdraw.StatusChoices.DONE)
