from abc import ABC, abstractmethod
from decimal import Decimal

from attr import dataclass

from payments.models import Payment


@dataclass
class PaymentFeeDTO:
    """
    Dataclass representing a fee.
    """

    amount_without_fee: Decimal
    amount_with_fee: Decimal
    fee_group_amount: Decimal
    fee_service_amount: Decimal


class IPaymentFeeService(ABC):
    @abstractmethod
    def calculate_fee(self, payment: Payment) -> PaymentFeeDTO:
        """
        Calculate fee for a payment.

        Args:
            payment(Payment): payment instance

        Returns:
            PaymentFeeDTO: payment fee
        """
        pass


class BasePaymentFeeService(IPaymentFeeService):
    """
    Service for calculating fees.
    """

    @staticmethod
    def _calculate_fee_group_amount(payment: Payment) -> Decimal:
        """
        Calculate fee_group for a payment.

        Args:
            payment(Payment): payment instance

        Returns:
            Decimal: fee amount
        """

        return round(payment.fee_group.sale_percent * payment.sale.amount / 100, 2)

    @staticmethod
    def _calculate_fee_service_amount(payment: Payment) -> Decimal:
        """
        Calculate fee_service for a payment.

        Args:
            payment(Payment): payment instance

        Returns:
            Decimal: fee amount
        """

        service_fee_amount = round(
            payment.fee_service.service_fee * payment.sale.amount / 100, 2
        )
        payment_fee_amount = round(
            payment.fee_service.payment_fee * payment.sale.amount / 100, 2
        )

        return service_fee_amount + payment_fee_amount

    def calculate_fee(self, payment: Payment) -> PaymentFeeDTO:
        """
        Calculate fee for a payment.

        Args:
            payment(Payment): payment instance

        Returns:
            PaymentFeeDTO: payment fee
        """
        amount_without_fee = payment.sale.amount

        fee_group_amount = self._calculate_fee_group_amount(payment)
        fee_service_amount = self._calculate_fee_service_amount(payment)

        amount_with_fee = amount_without_fee + fee_service_amount

        return PaymentFeeDTO(
            amount_without_fee=amount_without_fee,
            amount_with_fee=amount_with_fee,
            fee_group_amount=fee_group_amount,
            fee_service_amount=fee_service_amount,
        )
