import logging
from abc import ABC, abstractmethod

from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import Payment, Transaction
from payments.models.withdraw import Withdraw

logger = logging.getLogger(__name__)


class ITransactionService(ABC):
    @abstractmethod
    def create_payment_transaction(self, payment: Payment) -> Transaction:
        """
        Create transaction for a payment.

        Args:
            payment(Payment): payment instance
        """
        pass

    @abstractmethod
    def create_withdraw_transaction(self, withdraw: Withdraw) -> Transaction:
        """
        Create transaction for a withdraw.

        Args:
            withdraw(Withdraw): withdraw instance
        """
        pass


class TransactionService(ITransactionService):
    def create_payment_transaction(self, payment: Payment) -> Transaction:
        """
        Create transaction for a payment.

        Args:
            payment(Payment): payment instance
        """
        if not payment.session_id:
            logger.error("Payment session_id is not set for payment: {payment.id}")

            raise PaymentWithoutSessionIdError

        if hasattr(payment, "transaction") and payment.transaction:
            return payment.transaction

        amount = payment.amount - payment.fee_service_amount - payment.fee_group_amount

        return Transaction.objects.create(
            user=payment.sale.seller,
            type=Transaction.TransactionType.SALE,
            amount=amount,
        )

    def create_withdraw_transaction(self, withdraw: Withdraw) -> Transaction:
        """
        Create transaction for a withdraw.

        Args:
            withdraw(Withdraw): withdraw instance
        """
        if hasattr(withdraw, "transaction") and withdraw.transaction:
            return withdraw.transaction

        return Transaction.objects.create(
            user=withdraw.user,
            type=Transaction.TransactionType.WITHDRAW,
            amount=-withdraw.amount,
        )
