import logging
from abc import ABC, abstractmethod
from functools import partial

from django.db import transaction

from payments.exceptions.payment_service_exceptions import (
    PaymentServiceMissingFeeServiceError,
    PaymentServiceProcessingError,
    PaymentServiceUserWithoutFeeGroupError,
)
from payments.exceptions.processing_exceptions import ProcessingError
from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import FeeService, Payment
from payments.services.payment_fee_service import (
    BasePaymentFeeService,
    IPaymentFeeService,
)
from payments.services.processing_service import (
    IProcessingService,
)
from payments.services.transaction_service import (
    ITransactionService,
    TransactionService,
)
from payments.tasks import send_payment_transaction_notification
from realtime.enums import EventTypeEnum
from realtime.services import broadcast_event
from sales.models import Sale
from sales.serializers import SaleListSerializer

logger = logging.getLogger(__name__)


class IPaymentService(ABC):
    @abstractmethod
    def create_payment(self, sale: Sale, site_url: str) -> Payment:
        """
        Create payment for a sale.

        Args:
            sale(Sale): sale instance
            site_url(str): site url for callback

        Returns:
            Payment: payment instance
        """
        pass

    @abstractmethod
    def update_payment_status(self, payment: Payment) -> Payment:
        """
        Update payment status.

        Args:
            payment:

        Returns:
            Payment: payment instance
        """


class PaymentService(IPaymentService):
    def __init__(
        self,
        processing_service: IProcessingService,
        fee_service: IPaymentFeeService | None = None,
        transaction_service: ITransactionService | None = None,
    ):
        self.processing_service = processing_service
        self.fee_service = fee_service or BasePaymentFeeService()
        self.transaction_service = transaction_service or TransactionService()

    def create_payment(self, sale: Sale, site_url: str) -> Payment:
        """
        Create payment for a sale.

        Args:
            sale(Sale): sale instance
            site_url(str): site url for callback

        Returns:
            Payment: payment instance
        """
        with transaction.atomic():
            fee_group = sale.seller.fee_group
            if not fee_group:
                logger.error(f"Seller {sale.seller.id} missing fee group")

                raise PaymentServiceUserWithoutFeeGroupError

            fee_service = FeeService.objects.first()
            if not fee_service:
                logger.error("Missing fee service")

                raise PaymentServiceMissingFeeServiceError

            payment = Payment(
                sale=sale,
                fee_group=fee_group,
                fee_service=fee_service,
            )

            payment_fee = self.fee_service.calculate_fee(payment)
            payment.amount = payment_fee.amount_with_fee
            payment.fee_group_amount = payment_fee.fee_group_amount
            payment.fee_service_amount = payment_fee.fee_service_amount

            payment.save()

            try:
                session_id, session_url = self.processing_service.create_session(
                    payment=payment, site_url=site_url
                )
            except ProcessingError as e:
                logger.error(e, exc_info=True)

                raise PaymentServiceProcessingError(
                    "Payment processing error during session creating"
                )

            payment.session_id = session_id
            payment.session_url = session_url
            payment.save()

        return payment

    def update_payment_status(self, payment: Payment) -> Payment:
        """
        Update payment status.

        Args:
            payment(Payment): payment instance

        Returns:
            Payment: payment instance
        """
        with transaction.atomic():
            payment = Payment.objects.select_for_update().get(id=payment.id)

            try:
                new_payment_status = self.processing_service.get_session_status(payment)

                if new_payment_status == payment.status:
                    return payment

                payment.status = new_payment_status

                if payment.status is Payment.StatusChoices.PAID and (
                    not hasattr(payment, "transaction") or not payment.transaction
                ):
                    new_transaction = (
                        self.transaction_service.create_payment_transaction(payment)
                    )
                    payment.transaction = new_transaction

                    transaction.on_commit(
                        partial(
                            send_payment_transaction_notification.delay,
                            payment_id=str(payment.id),
                        )
                    )
            except ProcessingError as e:
                logger.error(e, exc_info=True)

                raise PaymentServiceProcessingError(
                    "Payment processing error during session status checking"
                )
            except PaymentWithoutSessionIdError:
                payment.status = Payment.StatusChoices.FAILED

            payment.save()

        broadcast_event(
            user_id=str(payment.sale.buyer.id),
            event_type=EventTypeEnum.SALE_UPDATED,
            payload=SaleListSerializer(payment.sale).data,
        )

        return payment
