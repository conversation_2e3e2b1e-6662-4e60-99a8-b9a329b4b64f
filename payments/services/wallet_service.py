from abc import ABC, abstractmethod
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.db.models import Exists, F, OuterRef, Q, Sum
from django.db.models.functions import Abs

from accounts.models import FeeGroup
from payments.dtos.wallet_dto import WalletDTO
from payments.models import Payment, Transaction
from payments.models.withdraw import Withdraw

User = get_user_model()


class IWalletService(ABC):
    @abstractmethod
    def get_wallet_dto(self, user: User) -> WalletDTO:
        pass

    @abstractmethod
    def get_user_balance(self, user: User) -> Decimal:
        pass

    @abstractmethod
    def get_group_balance(self, fee_group: FeeGroup) -> Decimal:
        pass

    @abstractmethod
    def get_last_user_withdraw(self, user: User) -> Withdraw | None:
        pass


class WalletService:
    @staticmethod
    def get_last_user_withdraw(user: User) -> Withdraw | None:
        """
        Retrieve the most recent withdrawal made by the user.

        This method fetches the latest withdrawal record based on the
        'created_at' timestamp for the given user.

        Args:
            user (User): The user whose last withdrawal is being requested.

        Returns:
            Withdraw | None: The latest withdrawal if it exists,
            otherwise None if no withdrawals are found.
        """
        try:
            return user.withdraws.latest("created_at")
        except Withdraw.DoesNotExist:
            return None

    @staticmethod
    def get_user_balance(user: User) -> Decimal:
        """
        Calculate the current balance of the user based on relevant transactions.

        This method considers withdrawals with 'DONE' or 'PENDING' statuses
        and payments marked as 'PAID'. It aggregates the amount of
        those transactions to compute the user's balance.

        Args:
            user (User): The user whose balance is being calculated.

        Returns:
            Decimal: The calculated balance of the user.
        """
        return (
            Transaction.objects.filter(
                (
                    Q(
                        withdraw__status__in=[
                            Withdraw.StatusChoices.DONE,
                            Withdraw.StatusChoices.PENDING,
                        ]
                    )
                    | Q(payment__status=Payment.StatusChoices.PAID)
                ),
                user=user,
            )
            .aggregate(balance=Sum("amount"))
            .get("balance")
        ) or Decimal("0.00")

    def get_wallet_dto(self, user: User) -> WalletDTO:
        """
        Generate a WalletDTO containing the user's balance and last withdrawal information.

        This method creates and returns a WalletDTO object that includes the user's
        balance (calculated from their transactions) and their most recent withdrawal.

        Args:
            user (User): The user for whom the WalletDTO is being generated.

        Returns:
            WalletDTO: An object containing the user's balance and last withdrawal.
        """
        last_withdraw = self.get_last_user_withdraw(user=user)
        balance = self.get_user_balance(user=user)

        return WalletDTO(
            balance=balance,
            last_withdraw=last_withdraw,
        )

    @staticmethod
    def get_group_balance(fee_group: FeeGroup) -> Decimal:
        """
        Calculate the total balance of all users in a specific fee group,
        including pending withdraw amounts but excluding transactions of users
        who have pending withdraws.
        """
        pending_amount = Transaction.objects.filter(
            withdraw__status=Withdraw.StatusChoices.PENDING,
            user__fee_group=fee_group,
        ).aggregate(total=Sum(Abs(F("amount"))))["total"] or Decimal("0.00")

        pending_withdraw_subquery = Transaction.objects.filter(
            withdraw__status=Withdraw.StatusChoices.PENDING,
            user__fee_group=fee_group,
            user=OuterRef("user"),
        )

        transactions_sum = Transaction.objects.filter(user__fee_group=fee_group).filter(
            Q(withdraw__status=Withdraw.StatusChoices.DONE)
            | Q(payment__status=Payment.StatusChoices.PAID)
        ).annotate(has_pending=Exists(pending_withdraw_subquery)).filter(
            has_pending=False
        ).aggregate(total=Sum("amount"))["total"] or Decimal("0.00")

        return transactions_sum + pending_amount
