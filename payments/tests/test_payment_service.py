from decimal import Decimal
from unittest.mock import MagicMock

from base.base_test_case import BaseTestCase
from base.factories import (
    FeeServiceFactory,
    PaymentFactory,
    SaleFactory,
    SaleTransactionFactory,
    UserFactory,
)
from payments.exceptions.payment_service_exceptions import (
    PaymentServiceMissingFeeServiceError,
    PaymentServiceProcessingError,
    PaymentServiceUserWithoutFeeGroupError,
)
from payments.exceptions.processing_exceptions import ProcessingError
from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import FeeService, Payment
from payments.services.payment_fee_service import IPaymentFeeService, PaymentFeeDTO
from payments.services.payment_service import PaymentService
from payments.services.processing_service import IProcessingService
from payments.services.transaction_service import ITransactionService
from payments.tasks import send_payment_transaction_notification


class PaymentServiceTests(BaseTestCase):
    def setUp(self):
        self.mock_processing_service = MagicMock(spec=IProcessingService)
        self.mock_fee_service = MagicMock(spec=IPaymentFeeService)
        self.mock_transaction_service = MagicMock(spec=ITransactionService)

        self.payment_service = PaymentService(
            processing_service=self.mock_processing_service,
            fee_service=self.mock_fee_service,
            transaction_service=self.mock_transaction_service,
        )

        self.fee_service_instance = FeeServiceFactory()
        self.seller = UserFactory()

    def test_create_payment_successful(self):
        """
        Test a successful payment creation flow.
        """
        sale = SaleFactory()

        payment_fee_mock = PaymentFeeDTO(
            amount_with_fee=Decimal(120),
            amount_without_fee=Decimal(100),
            fee_group_amount=Decimal(10),
            fee_service_amount=Decimal(5),
        )
        self.mock_fee_service.calculate_fee.return_value = payment_fee_mock

        session_id = "test_session_id"
        session_url = "https://payment-processor.example.com/session"
        self.mock_processing_service.create_session.return_value = (
            session_id,
            session_url,
        )

        payment = self.payment_service.create_payment(
            sale, site_url="https://testsite.com"
        )

        self.mock_fee_service.calculate_fee.assert_called_once_with(payment)
        self.mock_processing_service.create_session.assert_called_once_with(
            payment=payment,
            site_url="https://testsite.com",
        )

        self.assertEqual(payment.sale, sale)
        self.assertEqual(payment.amount, Decimal(120))
        self.assertEqual(payment.fee_group_amount, Decimal(10))
        self.assertEqual(payment.fee_service_amount, Decimal(5))
        self.assertEqual(payment.session_id, session_id)
        self.assertEqual(payment.session_url, session_url)

    def test_create_payment_missing_fee_group(self):
        """
        Test that creating a payment raises an error if the seller has no fee_group.
        """
        seller_no_fee_group = UserFactory(fee_group=None)
        sale = SaleFactory(seller=seller_no_fee_group)

        with self.assertRaises(PaymentServiceUserWithoutFeeGroupError):
            self.payment_service.create_payment(sale, site_url="https://testsite.com")

    def test_create_payment_missing_fee_service(self):
        """
        Test that creating a payment raises an error if FeeService does not exist in DB.
        """
        FeeService.objects.all().delete()

        sale = SaleFactory(seller=self.seller)
        with self.assertRaises(PaymentServiceMissingFeeServiceError):
            self.payment_service.create_payment(sale, site_url="https://testsite.com")

    def test_create_payment_processing_error(self):
        """
        Test that a processing error in create_session raises ServiceUnavailable.
        """
        sale = SaleFactory(seller=self.seller)

        payment_fee_mock = PaymentFeeDTO(
            amount_with_fee=Decimal(120),
            amount_without_fee=Decimal(100),
            fee_group_amount=Decimal(10),
            fee_service_amount=Decimal(5),
        )
        self.mock_fee_service.calculate_fee.return_value = payment_fee_mock

        # Make create_session raise ProcessingError
        self.mock_processing_service.create_session.side_effect = ProcessingError

        with self.assertRaises(PaymentServiceProcessingError):
            self.payment_service.create_payment(sale, site_url="https://testsite.com")

    def test_update_payment_status_paid(self):
        """
        Test that update_payment_status sets the status to PAID and creates a transaction
        if Payment is PAID without an existing transaction.
        """
        payment = PaymentFactory()
        SaleFactory(
            payment=payment,
        )
        transaction = SaleTransactionFactory()

        self.mock_processing_service.get_session_status.return_value = (
            Payment.StatusChoices.PAID
        )
        self.mock_transaction_service.create_payment_transaction.return_value = (
            transaction
        )

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            updated_payment = self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 1)
        self.assertEqual(callbacks[0].keywords, {"payment_id": str(payment.id)})
        self.assertEqual(callbacks[0].args, ())
        self.assertEqual(callbacks[0].func, send_payment_transaction_notification.delay)

        self.mock_processing_service.get_session_status.assert_called_once_with(payment)
        self.mock_transaction_service.create_payment_transaction.assert_called_once_with(
            payment
        )

        self.assertEqual(updated_payment.status, Payment.StatusChoices.PAID)
        self.assertEqual(updated_payment.transaction, transaction)

    def test_update_transaction_already_exists(self):
        """
        Test that update_payment_status does not create a new transaction if one already exists.
        """
        transaction = SaleTransactionFactory()
        payment = PaymentFactory(
            transaction=transaction, status=Payment.StatusChoices.PAID
        )
        SaleFactory(
            payment=payment,
        )

        self.mock_processing_service.get_session_status.return_value = (
            Payment.StatusChoices.PAID
        )

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            updated_payment = self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 0)

        self.mock_processing_service.get_session_status.assert_called_once_with(payment)
        self.mock_transaction_service.create_payment_transaction.assert_not_called()

        self.assertEqual(updated_payment.status, Payment.StatusChoices.PAID)
        self.assertEqual(updated_payment.transaction, transaction)

    def test_update_payment_status_pending(self):
        """
        Test that update_payment_status does not create a transaction for a pending payment.
        """
        payment = PaymentFactory()
        SaleFactory(
            payment=payment,
        )

        self.mock_processing_service.get_session_status.return_value = (
            Payment.StatusChoices.PENDING
        )

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            updated_payment = self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 0)

        self.mock_processing_service.get_session_status.assert_called_once_with(payment)
        self.mock_transaction_service.create_payment_transaction.assert_not_called()

        self.assertEqual(updated_payment.status, Payment.StatusChoices.PENDING)
        self.assertIsNone(updated_payment.transaction)

    def test_update_payment_status_expired(self):
        """
        Test that update_payment_status does not create a transaction for an expired payment.
        """
        payment = PaymentFactory()
        SaleFactory(
            payment=payment,
        )

        self.mock_processing_service.get_session_status.return_value = (
            Payment.StatusChoices.EXPIRED
        )

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            updated_payment = self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 0)

        self.mock_processing_service.get_session_status.assert_called_once_with(payment)
        self.mock_transaction_service.create_payment_transaction.assert_not_called()

        self.assertEqual(updated_payment.status, Payment.StatusChoices.EXPIRED)
        self.assertIsNone(updated_payment.transaction)

    def test_update_payment_status_processing_error(self):
        """
        Test that a ProcessingError in get_session_status raises ServiceUnavailable.
        """
        payment = PaymentFactory()

        self.mock_processing_service.get_session_status.side_effect = ProcessingError

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            with self.assertRaises(PaymentServiceProcessingError):
                self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 0)

    def test_update_payment_status_payment_without_session_id_error(self):
        """
        Test that PaymentWithoutSessionIdError sets status to EXPIRED.
        """
        payment = PaymentFactory()
        SaleFactory(
            payment=payment,
        )

        self.mock_processing_service.get_session_status.side_effect = (
            PaymentWithoutSessionIdError
        )

        with self.captureOnCommitCallbacks(execute=False) as callbacks:
            updated_payment = self.payment_service.update_payment_status(payment)

        self.assertEqual(len(callbacks), 0)

        self.assertEqual(updated_payment.status, Payment.StatusChoices.FAILED)
        self.mock_transaction_service.create_payment_transaction.assert_not_called()
