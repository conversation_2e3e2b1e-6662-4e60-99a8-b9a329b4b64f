from decimal import Decimal

from base.base_test_case import BaseTestCase
from base.factories import (
    FeeGroupFactory,
    FeeServiceFactory,
    PaymentFactory,
    SaleFactory,
)
from payments.services.payment_fee_service import BasePaymentFeeService, PaymentFeeDTO


class BasePaymentFeeServiceTests(BaseTestCase):
    def setUp(self):
        self.fee_service = BasePaymentFeeService()

    def test_calculate_fee_no_fees(self):
        """
        If both fee_group.sale_percent and fee_service fees are 0,
        the calculated fees should be 0 and amount_with_fee equals sale.amount.
        """
        fee_group = FeeGroupFactory(sale_percent=Decimal("0.0"))
        fee_service = FeeServiceFactory(
            service_fee=Decimal("0.0"), payment_fee=Decimal("0.0")
        )
        payment = PaymentFactory(
            fee_group=fee_group,
            fee_service=fee_service,
        )
        SaleFactory(amount=Decimal("100.00"), payment=payment)

        result = self.fee_service.calculate_fee(payment)

        self.assertIsInstance(result, PaymentFeeDTO)
        self.assertEqual(result.amount_without_fee, Decimal("100.00"))
        self.assertEqual(result.amount_with_fee, Decimal("100.00"))
        self.assertEqual(result.fee_group_amount, Decimal("0.00"))
        self.assertEqual(result.fee_service_amount, Decimal("0.00"))

    def test_calculate_fee_normal(self):
        """
        Test a normal scenario with non-zero fees.
        """
        fee_group = FeeGroupFactory(sale_percent=Decimal("7.0"))
        fee_service = FeeServiceFactory(
            service_fee=Decimal("10.0"), payment_fee=Decimal("5.0")
        )
        payment = PaymentFactory(
            fee_group=fee_group,
            fee_service=fee_service,
        )
        SaleFactory(amount=Decimal("100.00"), payment=payment)

        result = self.fee_service.calculate_fee(payment)

        self.assertEqual(result.amount_without_fee, Decimal("100.00"))
        self.assertEqual(result.fee_group_amount, Decimal("7.00"))
        self.assertEqual(result.fee_service_amount, Decimal("15.00"))
        self.assertEqual(result.amount_with_fee, Decimal("115.00"))
