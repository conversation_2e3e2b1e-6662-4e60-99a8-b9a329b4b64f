from unittest.mock import patch

from base.base_test_case import BaseTestCase
from base.factories import (
    FeeGroupTgConfigFactory,
    PaymentFactory,
    SaleFactory,
    SaleTransactionFactory,
)
from payments.exceptions.payment_tg_bot_service_exceptions import (
    PaymentTgBotServiceChatIdError,
    PaymentTgBotServiceTransactionError,
)
from payments.services.payment_tg_bot_service import PaymentTelegramBotService


class PaymentTelegramBotServiceTests(BaseTestCase):
    def setUp(self):
        self.service = PaymentTelegramBotService()
        self.transaction = SaleTransactionFactory()
        self.payment = PaymentFactory(transaction=self.transaction)
        self.sale = SaleFactory(payment=self.payment)
        self.fee_group = self.payment.sale.seller.fee_group
        self.tg_config = FeeGroupTgConfigFactory(fee_group=self.fee_group)

    def test_get_buyer_chat_id_missing_tg_config(self):
        """
        If the seller's fee group doesn't have a tg config, a PaymentTgBotServiceChatIdError should be raised.
        """
        self.payment.sale.seller.fee_group.tg_config.delete()
        self.payment.refresh_from_db()

        with self.assertRaises(PaymentTgBotServiceChatIdError):
            self.service._get_buyer_chat_id(self.payment)

    def test_get_buyer_chat_id(self):
        """
        If the seller's fee group has a tg config, the chat id should be returned.
        """
        chat_id = self.service._get_buyer_chat_id(self.payment)
        self.assertEqual(chat_id, self.tg_config.chat_id)

    def test_get_payment_notification_text_missing_transaction(self):
        """
        If the payment doesn't have a transaction, a PaymentTgBotServiceTransactionError should be raised.
        """
        self.payment.transaction = None
        self.payment.save()

        with self.assertRaises(PaymentTgBotServiceTransactionError):
            self.service._get_payment_notification_text(self.payment)

    @patch.object(PaymentTelegramBotService, "send_message")
    def test_successful_send_payment_transaction_notification(self, mock_send_message):
        """
        If the payment has a transaction and the seller's fee group has a tg config, the notification should be sent.
        """
        mock_send_message.return_value = None
        self.service.send_payment_transaction_notification(self.payment)

        mock_send_message.assert_called_once_with(
            chat_id=self.tg_config.chat_id,
            text=self.service._get_payment_notification_text(self.payment),
        )
