from decimal import Decimal
from unittest.mock import MagicMock

from django.test import TestCase, override_settings

from base.factories import (
    FeeGroupFactory,
    PendingGroupWithdrawFactory,
    PendingWithdrawFactory,
    UserFactory,
    WithdrawTransactionFactory,
)
from payments.dtos.wallet_dto import WalletDTO
from payments.exceptions.crypto_service_exceptions import CryptoServiceError
from payments.exceptions.withdraw_service_exceptions import (
    WithdrawServiceCryptoServiceError,
    WithdrawServiceGroupBalanceError,
    WithdrawServiceInsufficientBalanceError,
    WithdrawServiceInvalidAddressError,
    WithdrawServicePendingGroupWithdrawError,
    WithdrawServicePendingWithdrawError,
)
from payments.models.withdraw import GroupWithdraw, Withdraw
from payments.services.crypto_service import ICryptoService
from payments.services.transaction_service import ITransactionService
from payments.services.wallet_service import IWalletService
from payments.services.withdraw_service import WithdrawService


@override_settings(WITHDRAW_MIN_AMOUNT=Decimal("10.00"))
class WithdrawServiceTests(TestCase):
    def setUp(self):
        self.mock_crypto_service = MagicMock(spec=ICryptoService)
        self.mock_wallet_service = MagicMock(spec=IWalletService)
        self.mock_transaction_service = MagicMock(spec=ITransactionService)

        self.withdraw_service = WithdrawService(
            crypto_service=self.mock_crypto_service,
            wallet_service=self.mock_wallet_service,
            transaction_service=self.mock_transaction_service,
        )

        self.user = UserFactory()

    def test_create_withdraw_invalid_address(self):
        """
        If the crypto service validates an address as invalid,
        a ValidationError should be raised.
        """
        self.mock_crypto_service.validate_address.return_value = False

        with self.assertRaises(WithdrawServiceInvalidAddressError) as ctx:
            self.withdraw_service.create_withdraw(
                user=self.user,
                address="bad_address",
            )
        self.assertIn("Invalid address", str(ctx.exception))

        self.mock_crypto_service.validate_address.assert_called_once_with("bad_address")

    def test_create_withdraw_crypto_service_unavailable(self):
        """
        If the crypto service raises a CryptoServiceError,
        a ServiceUnavailable should be raised.
        """
        self.mock_crypto_service.validate_address.side_effect = CryptoServiceError(
            "Some error"
        )

        with self.assertRaises(WithdrawServiceCryptoServiceError):
            self.withdraw_service.create_withdraw(
                user=self.user,
                address="some_address",
            )

        self.mock_crypto_service.validate_address.assert_called_once()

    def test_create_withdraw_insufficient_balance(self):
        """
        If the user’s wallet balance <= WITHDRAW_MIN_AMOUNT,
        a ValidationError is raised.
        """
        self.mock_crypto_service.validate_address.return_value = True

        wallet_dto = WalletDTO(balance=Decimal("5.00"), last_withdraw=None)
        self.mock_wallet_service.get_wallet_dto.return_value = wallet_dto

        with self.assertRaises(WithdrawServiceInsufficientBalanceError) as ctx:
            self.withdraw_service.create_withdraw(
                user=self.user,
                address="some_address",
            )
        self.assertIn("Current balance is not enough", str(ctx.exception))

        self.mock_crypto_service.validate_address.assert_called_once_with(
            "some_address"
        )

    def test_create_withdraw_already_pending(self):
        """
        If the user already has a pending withdrawal,
        a ValidationError is raised.
        """
        self.mock_crypto_service.validate_address.return_value = True

        pending_withdraw = PendingWithdrawFactory(
            user=self.user,
        )
        wallet_dto = WalletDTO(
            balance=Decimal("100.00"), last_withdraw=pending_withdraw
        )
        self.mock_wallet_service.get_wallet_dto.return_value = wallet_dto

        with self.assertRaises(WithdrawServicePendingWithdrawError):
            self.withdraw_service.create_withdraw(
                user=self.user,
                address="some_address",
            )

        self.mock_crypto_service.validate_address.assert_called_once_with(
            "some_address"
        )

    def test_create_withdraw_successful(self):
        """
        If address is valid, balance is sufficient, and there is no pending withdrawal,
        a Withdraw is created, and a transaction is created for that withdraw.
        """
        self.mock_crypto_service.validate_address.return_value = True

        wallet_dto = WalletDTO(balance=Decimal("100.00"), last_withdraw=None)
        self.mock_wallet_service.get_wallet_dto.return_value = wallet_dto

        transaction = WithdrawTransactionFactory(
            amount=Decimal("100.00"),
        )
        self.mock_transaction_service.create_withdraw_transaction.return_value = (
            transaction
        )

        withdraw = self.withdraw_service.create_withdraw(
            user=self.user,
            address="valid_address",
        )

        self.assertIsNotNone(withdraw.pk)
        self.assertEqual(withdraw.user, self.user)
        self.assertEqual(withdraw.amount, Decimal("100.00"))
        self.assertEqual(withdraw.address, "valid_address")
        self.assertEqual(withdraw.transaction, transaction)
        self.mock_crypto_service.validate_address.assert_called_once_with(
            "valid_address"
        )
        self.mock_wallet_service.get_wallet_dto.assert_called_once_with(user=self.user)
        self.mock_transaction_service.create_withdraw_transaction.assert_called_once_with(
            withdraw=withdraw
        )


@override_settings(WITHDRAW_MIN_AMOUNT=Decimal("10.00"))
class GroupWithdrawServiceTests(TestCase):
    def setUp(self):
        self.mock_crypto_service = MagicMock(spec=ICryptoService)
        self.mock_wallet_service = MagicMock(spec=IWalletService)
        self.mock_transaction_service = MagicMock(spec=ITransactionService)

        self.withdraw_service = WithdrawService(
            crypto_service=self.mock_crypto_service,
            wallet_service=self.mock_wallet_service,
            transaction_service=self.mock_transaction_service,
        )

        self.fee_group = FeeGroupFactory(name="New Group", sale_percent=Decimal("10.0"))

    def test_create_group_withdraw_invalid_address(self):
        """
        If the crypto service validates an address as invalid,
        a WithdrawServiceInvalidAddressError should be raised.
        """
        self.mock_crypto_service.validate_address.return_value = False

        with self.assertRaises(WithdrawServiceInvalidAddressError) as ctx:
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="bad_address",
                amount=Decimal("100.00"),
            )
        self.assertIn("Error validating address", str(ctx.exception))

        self.mock_crypto_service.validate_address.assert_called_once_with("bad_address")

    def test_create_group_withdraw_crypto_service_unavailable(self):
        """
        If the crypto service raises a CryptoServiceError,
        a WithdrawServiceCryptoServiceError should be raised.
        """
        self.mock_crypto_service.validate_address.side_effect = CryptoServiceError(
            "Some error"
        )

        with self.assertRaises(WithdrawServiceCryptoServiceError):
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="some_address",
                amount=Decimal("100.00"),
            )

        self.mock_crypto_service.validate_address.assert_called_once()

    def test_create_group_withdraw_already_pending(self):
        """
        If the fee group already has a pending group withdrawal,
        a WithdrawServicePendingGroupWithdrawError is raised.
        """
        # Create a pending group withdrawal for this fee group
        PendingGroupWithdrawFactory(fee_group=self.fee_group)

        with self.assertRaises(WithdrawServicePendingGroupWithdrawError):
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="some_address",
                amount=Decimal("100.00"),
            )

    def test_create_group_withdraw_insufficient_balance(self):
        """
        If the group balance <= WITHDRAW_MIN_AMOUNT,
        a WithdrawServiceInsufficientBalanceError is raised.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("5.00")

        with self.assertRaises(WithdrawServiceInsufficientBalanceError) as ctx:
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="some_address",
                amount=Decimal("5.00"),
            )
        self.assertIn("Current balance is not enough", str(ctx.exception))

        self.mock_crypto_service.validate_address.assert_called_once_with(
            "some_address"
        )
        self.mock_wallet_service.get_group_balance.assert_called_once_with(
            fee_group=self.fee_group
        )

    def test_create_group_withdraw_balance_mismatch(self):
        """
        If the requested amount doesn't match the group balance,
        a WithdrawServiceGroupBalanceError is raised.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("100.00")

        with self.assertRaises(WithdrawServiceGroupBalanceError) as ctx:
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="some_address",
                amount=Decimal("50.00"),
            )
        self.assertIn(
            "Group balance 100.00 doesn't match requested amount 50.00",
            str(ctx.exception),
        )

        self.mock_crypto_service.validate_address.assert_called_once_with(
            "some_address"
        )
        self.mock_wallet_service.get_group_balance.assert_called_once_with(
            fee_group=self.fee_group
        )

    def test_create_group_withdraw_successful_with_existing_pending_withdrawals(self):
        """
        If users have existing pending withdrawals, they should be updated
        with the new address and linked to the group withdrawal.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("200.00")

        # Create users in the fee group
        user1 = UserFactory(fee_group=self.fee_group)
        user2 = UserFactory(fee_group=self.fee_group)

        # Create existing pending withdrawals
        pending_withdraw1 = PendingWithdrawFactory(user=user1, amount=Decimal("100.00"))
        pending_withdraw2 = PendingWithdrawFactory(user=user2, amount=Decimal("100.00"))

        # Mock wallet service methods
        self.mock_wallet_service.get_last_user_withdraw.side_effect = [
            pending_withdraw1,
            pending_withdraw2,
        ]
        self.mock_wallet_service.get_user_balance.side_effect = [
            Decimal("100.00"),
            Decimal("100.00"),
        ]

        group_withdraw = self.withdraw_service.create_group_withdraw(
            fee_group=self.fee_group,
            address="valid_address",
            amount=Decimal("200.00"),
        )

        # Verify group withdrawal was created
        self.assertIsNotNone(group_withdraw.pk)
        self.assertEqual(group_withdraw.fee_group, self.fee_group)
        self.assertEqual(group_withdraw.amount, Decimal("200.00"))
        self.assertEqual(group_withdraw.address, "valid_address")
        self.assertEqual(group_withdraw.status, GroupWithdraw.StatusChoices.PENDING)

        # Verify existing withdrawals were updated
        pending_withdraw1.refresh_from_db()
        pending_withdraw2.refresh_from_db()
        self.assertEqual(pending_withdraw1.address, "valid_address")
        self.assertEqual(pending_withdraw1.group_withdraw, group_withdraw)
        self.assertEqual(pending_withdraw2.address, "valid_address")
        self.assertEqual(pending_withdraw2.group_withdraw, group_withdraw)

        # Verify service calls
        self.mock_crypto_service.validate_address.assert_called_once_with(
            "valid_address"
        )
        self.mock_wallet_service.get_group_balance.assert_called_once_with(
            fee_group=self.fee_group
        )

    def test_create_group_withdraw_successful_with_new_withdrawals(self):
        """
        If users don't have pending withdrawals, new withdrawals should be created
        and linked to the group withdrawal.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("150.00")

        # Create users in the fee group
        UserFactory(fee_group=self.fee_group)
        UserFactory(fee_group=self.fee_group)

        # Mock wallet service methods - no existing withdrawals
        self.mock_wallet_service.get_last_user_withdraw.return_value = None
        self.mock_wallet_service.get_user_balance.side_effect = [
            Decimal("75.00"),
            Decimal("75.00"),
        ]

        # Mock transaction creation
        transaction1 = WithdrawTransactionFactory(amount=Decimal("-75.00"))
        transaction2 = WithdrawTransactionFactory(amount=Decimal("-75.00"))
        self.mock_transaction_service.create_withdraw_transaction.side_effect = [
            transaction1,
            transaction2,
        ]

        group_withdraw = self.withdraw_service.create_group_withdraw(
            fee_group=self.fee_group,
            address="valid_address",
            amount=Decimal("150.00"),
        )

        # Verify group withdrawal was created
        self.assertIsNotNone(group_withdraw.pk)
        self.assertEqual(group_withdraw.fee_group, self.fee_group)
        self.assertEqual(group_withdraw.amount, Decimal("150.00"))
        self.assertEqual(group_withdraw.address, "valid_address")
        self.assertEqual(group_withdraw.status, GroupWithdraw.StatusChoices.PENDING)

        # Verify new withdrawals were created
        created_withdrawals = group_withdraw.withdraws.all()
        self.assertEqual(created_withdrawals.count(), 2)

        # Verify service calls
        self.mock_crypto_service.validate_address.assert_called_once_with(
            "valid_address"
        )
        self.mock_wallet_service.get_group_balance.assert_called_once_with(
            fee_group=self.fee_group
        )
        self.assertEqual(
            self.mock_transaction_service.create_withdraw_transaction.call_count, 2
        )

    def test_create_group_withdraw_skips_users_with_zero_balance(self):
        """
        Users with zero balance should be skipped during group withdrawal creation.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("100.00")

        # Create users in the fee group
        UserFactory(fee_group=self.fee_group)
        UserFactory(fee_group=self.fee_group)
        UserFactory(fee_group=self.fee_group)

        # Mock wallet service methods - user2 has zero balance
        self.mock_wallet_service.get_last_user_withdraw.return_value = None
        self.mock_wallet_service.get_user_balance.side_effect = [
            Decimal("50.00"),
            Decimal("0.00"),
            Decimal("50.00"),
        ]

        # Mock transaction creation
        transaction1 = WithdrawTransactionFactory(amount=Decimal("-50.00"))
        transaction2 = WithdrawTransactionFactory(amount=Decimal("-50.00"))
        self.mock_transaction_service.create_withdraw_transaction.side_effect = [
            transaction1,
            transaction2,
        ]

        group_withdraw = self.withdraw_service.create_group_withdraw(
            fee_group=self.fee_group,
            address="valid_address",
            amount=Decimal("100.00"),
        )

        # Verify group withdrawal was created
        self.assertIsNotNone(group_withdraw.pk)
        self.assertEqual(group_withdraw.amount, Decimal("100.00"))

        # Verify only 2 withdrawals were created (user2 with zero balance was skipped)
        created_withdrawals = group_withdraw.withdraws.all()
        self.assertEqual(created_withdrawals.count(), 2)

        # Verify only 2 transactions were created
        self.assertEqual(
            self.mock_transaction_service.create_withdraw_transaction.call_count, 2
        )

    def test_create_group_withdraw_balance_validation_error(self):
        """
        If the sum of individual withdrawals doesn't match the group balance,
        a WithdrawServiceGroupBalanceError should be raised.
        """
        self.mock_crypto_service.validate_address.return_value = True
        self.mock_wallet_service.get_group_balance.return_value = Decimal("100.00")
        self.mock_transaction_service.create_withdraw_transaction.side_effect = (
            lambda *args, **kwargs: WithdrawTransactionFactory()
        )

        # Create users in the fee group
        UserFactory(fee_group=self.fee_group)

        # Mock wallet service methods - user balance doesn't match group balance
        self.mock_wallet_service.get_last_user_withdraw.return_value = None
        self.mock_wallet_service.get_user_balance.return_value = Decimal(
            "50.00"
        )  # Less than group balance

        with self.assertRaises(WithdrawServiceGroupBalanceError) as ctx:
            self.withdraw_service.create_group_withdraw(
                fee_group=self.fee_group,
                address="valid_address",
                amount=Decimal("100.00"),
            )
        self.assertIn(
            "Group balance doesn't match sum of withdraws", str(ctx.exception)
        )

    def test_mark_group_withdraw_as_done_successful(self):
        """
        When marking a group withdrawal as done, the group withdrawal status
        should be updated to DONE and all associated withdrawals should also
        be marked as DONE.
        """

        group_withdraw = PendingGroupWithdrawFactory(fee_group=self.fee_group)

        # Create some associated withdrawals
        user1 = UserFactory(fee_group=self.fee_group)
        user2 = UserFactory(fee_group=self.fee_group)
        withdraw1 = PendingWithdrawFactory(user=user1, group_withdraw=group_withdraw)
        withdraw2 = PendingWithdrawFactory(user=user2, group_withdraw=group_withdraw)

        # Mark the group withdrawal as done
        self.withdraw_service.mark_group_withdraw_as_done(group_withdraw)

        # Verify group withdrawal status was updated
        group_withdraw.refresh_from_db()
        self.assertEqual(group_withdraw.status, GroupWithdraw.StatusChoices.DONE)

        # Verify all associated withdrawals were marked as done
        withdraw1.refresh_from_db()
        withdraw2.refresh_from_db()
        self.assertEqual(withdraw1.status, Withdraw.StatusChoices.DONE)
        self.assertEqual(withdraw2.status, Withdraw.StatusChoices.DONE)
