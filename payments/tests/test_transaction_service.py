from decimal import Decimal

from base.base_test_case import BaseTestCase
from base.factories import (
    DoneWithdrawFactory,
    PaymentFactory,
    PendingWithdrawFactory,
    SaleFactory,
    SaleTransactionFactory,
    WithdrawTransactionFactory,
)
from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import Transaction
from payments.services.transaction_service import TransactionService


class TransactionServiceTests(BaseTestCase):
    def setUp(self):
        self.transaction_service = TransactionService()

    def test_create_payment_transaction_no_session_id_raises_error(self):
        """
        If payment.session_id is not set, PaymentWithoutSessionIdError should be raised.
        """
        payment = PaymentFactory()
        payment.session_id = None

        with self.assertRaises(PaymentWithoutSessionIdError):
            self.transaction_service.create_payment_transaction(payment)

    def test_create_payment_transaction_already_has_transaction(self):
        """
        If Payment already has a transaction, return the existing transaction (no new one is created).
        """
        existing_transaction = SaleTransactionFactory()
        payment = PaymentFactory(transaction=existing_transaction)

        returned_transaction = self.transaction_service.create_payment_transaction(
            payment
        )

        self.assertEqual(returned_transaction, existing_transaction)
        self.assertEqual(Transaction.objects.count(), 1)

    def test_create_payment_transaction_creates_new_transaction(self):
        """
        If Payment has no existing transaction, create a new one.
        """
        payment = PaymentFactory(
            amount=Decimal("100"),
            fee_service_amount=Decimal("10"),
            fee_group_amount=Decimal("5"),
            transaction=None,
        )
        sale = SaleFactory(payment=payment)

        returned_transaction = self.transaction_service.create_payment_transaction(
            payment
        )
        self.assertIsInstance(returned_transaction, Transaction)

        self.assertEqual(returned_transaction.user, sale.seller)
        self.assertEqual(returned_transaction.type, Transaction.TransactionType.SALE)

        # The net amount is payment.amount - fee_service_amount - fee_group_amount
        expected_net_amount = Decimal("100") - Decimal("10") - Decimal("5")
        self.assertEqual(returned_transaction.amount, expected_net_amount)

    def test_create_withdraw_transaction_already_has_transaction(self):
        """
        If a Withdraw already has a transaction, return it instead of creating a new one.
        """
        existing_transaction = WithdrawTransactionFactory()
        withdraw = DoneWithdrawFactory(transaction=existing_transaction)

        returned_transaction = self.transaction_service.create_withdraw_transaction(
            withdraw
        )
        self.assertEqual(returned_transaction, existing_transaction)

        self.assertEqual(Transaction.objects.count(), 1)

    def test_create_withdraw_transaction_creates_new_transaction(self):
        """
        If a Withdraw does not have a transaction, create one.
        """
        withdraw = PendingWithdrawFactory(amount=Decimal("200"), transaction=None)

        returned_transaction = self.transaction_service.create_withdraw_transaction(
            withdraw
        )
        self.assertIsInstance(returned_transaction, Transaction)
        self.assertEqual(returned_transaction.user, withdraw.user)
        self.assertEqual(
            returned_transaction.type, Transaction.TransactionType.WITHDRAW
        )

        # For a withdraw, amount is stored as a negative in Transaction
        self.assertEqual(returned_transaction.amount, Decimal("-200"))
