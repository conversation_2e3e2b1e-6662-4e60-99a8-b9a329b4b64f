import math
from decimal import Decimal
from unittest.mock import MagicMock, patch

import httpx
import stripe
from django.core.exceptions import ImproperlyConfigured
from django.test import override_settings

from base.base_test_case import BaseTestCase
from base.factories import PaymentFactory, SaleFactory
from payments.exceptions.processing_exceptions import (
    PlategaCreateSessionError,
    PlategaRetrieveSessionError,
    SafonAuthError,
    SafonCreateSessionError,
    SafonRetrieveSessionError,
    StripeCreateSessionError,
    StripeRetrieveSessionError,
)
from payments.exceptions.transaction_exceptions import PaymentWithoutSessionIdError
from payments.models import Payment
from payments.services.processing_service import (
    PlategaProcessingService,
    SafonProcessingService,
    StripeProcessingService,
)


class StripeProcessingServiceTests(BaseTestCase):
    def setUp(self):
        """
        Set up the test case by creating a PaymentFactory instance.
        """
        self.payment = PaymentFactory()
        self.sale = SaleFactory(payment=self.payment)
        self.service = StripeProcessingService()

    def test_init_no_secret_key_raises_error(self):
        """
        If STRIPE_SECRET_KEY is not set, the service should raise ImproperlyConfigured.
        """
        with override_settings(STRIPE_SECRET_KEY=None):
            with self.assertRaises(ImproperlyConfigured):
                StripeProcessingService()

    @patch("stripe.checkout.Session.create")
    def test_create_session_success(self, mock_stripe_create):
        """
        Test successful creation of a Stripe session.
        """
        mock_stripe_create.return_value = MagicMock(
            id="session_test_id", url="https://stripe.com/test-session"
        )

        site_url = "https://example.com"

        session_id, session_url = self.service.create_session(
            payment=self.payment,
            site_url=site_url,
        )

        self.assertEqual(session_id, "session_test_id")
        self.assertEqual(session_url, "https://stripe.com/test-session")

        mock_stripe_create.assert_called_once()
        _, kwargs = mock_stripe_create.call_args

        # For instance, check line_items contain the correct amount in cents
        expected_fee_amount_cents = int(self.payment.fee_service_amount * 100)
        expected_lot_amount_cents = (
            int(self.payment.amount * 100) - expected_fee_amount_cents
        )
        lot_item = kwargs["line_items"][0]
        self.assertEqual(
            lot_item["price_data"]["unit_amount"], expected_lot_amount_cents
        )
        fee_item = kwargs["line_items"][1]
        self.assertEqual(
            fee_item["price_data"]["unit_amount"], expected_fee_amount_cents
        )

        # Check success/cancel URLs
        success_url = kwargs["success_url"]
        self.assertIn("/orders", success_url)

        cancel_url = kwargs["cancel_url"]
        self.assertIn(f"/payment-cancel/{str(self.payment.id)}", cancel_url)

    @patch(
        "stripe.checkout.Session.create",
        side_effect=stripe.error.StripeError("Something went wrong"),
    )
    def test_create_session_stripe_error(self, mock_stripe_create):
        """
        If stripe raises a StripeError, our service should raise StripeCreateSessionError.
        """
        site_url = "https://example.com"

        with self.assertRaises(StripeCreateSessionError) as cm:
            self.service.create_session(self.payment, site_url)

        self.assertIn("Stripe error during session creating", str(cm.exception))
        mock_stripe_create.assert_called_once()

    def test_get_session_status_no_session_id(self):
        """
        If payment.session_id is not set, the service should raise PaymentWithoutSessionIdError.
        """
        self.payment.session_id = None  # No session ID

        with self.assertRaises(PaymentWithoutSessionIdError):
            self.service.get_session_status(self.payment)

    @patch("stripe.checkout.Session.retrieve")
    def test_get_session_status_paid(self, mock_stripe_retrieve):
        """
        If the retrieved session.status is 'complete', return Payment.StatusChoices.PAID.
        """
        mock_stripe_retrieve.return_value = MagicMock(status="complete")

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.PAID)
        mock_stripe_retrieve.assert_called_once_with(str(self.payment.session_id))

    @patch("stripe.checkout.Session.retrieve")
    def test_get_session_status_expired(self, mock_stripe_retrieve):
        """
        If the retrieved session.status is 'expired', return Payment.StatusChoices.EXPIRED.
        """
        mock_stripe_retrieve.return_value = MagicMock(status="expired")

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.EXPIRED)

    @patch("stripe.checkout.Session.retrieve")
    def test_get_session_status_pending(self, mock_stripe_retrieve):
        """
        If the retrieved session.status is something other than 'complete' or 'expired', return PENDING.
        """
        mock_stripe_retrieve.return_value = MagicMock(status="processing")

        self.status = self.service.get_session_status(self.payment)
        self.assertEqual(self.status, Payment.StatusChoices.PENDING)

    @patch(
        "stripe.checkout.Session.retrieve",
        side_effect=stripe.error.StripeError("Cannot retrieve session"),
    )
    def test_get_session_status_stripe_error(self, mock_stripe_retrieve):
        """
        If stripe raises a StripeError when retrieving the session, our service should raise StripeRetrieveSessionError.
        """
        with self.assertRaises(StripeRetrieveSessionError) as cm:
            self.service.get_session_status(self.payment)

        self.assertIn("Stripe error during session status checking", str(cm.exception))
        mock_stripe_retrieve.assert_called_once_with(str(self.payment.session_id))


class PlategaProcessingServiceTests(BaseTestCase):
    def setUp(self):
        """
        Set up the test case by creating a PaymentFactory instance.
        """
        self.payment = PaymentFactory()
        self.sale = SaleFactory(payment=self.payment)
        self.service = PlategaProcessingService()

    def test_init_missing_settings_raises_error(self):
        """
        If any required Platega setting is not set, the service should raise ImproperlyConfigured.
        """
        for key in [
            "PLATEGA_SECRET_KEY",
            "PLATEGA_BASE_URL",
            "PLATEGA_PAYMENT_METHOD",
            "PLATEGA_MERCHANT_ID",
            "MAX_LOT_AMOUNT",
        ]:
            with override_settings(**{key: None}):
                with self.assertRaises(ImproperlyConfigured):
                    PlategaProcessingService()

    def test_get_headers(self):
        """
        Test that _get_headers returns the correct headers.
        """
        headers = self.service._get_headers()
        self.assertEqual(headers["X-Secret"], self.service.api_key)
        self.assertEqual(headers["X-MerchantId"], self.service.merchant_id)
        self.assertEqual(headers["Content-Type"], "application/json")

    @patch("httpx.post")
    def test_create_session_success(self, mock_httpx_post):
        """
        Test successful creation of a Platega session.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "transactionId": "transaction_test_id",
            "redirect": "https://platega.io/test-session",
        }
        mock_response.raise_for_status.return_value = None
        mock_httpx_post.return_value = mock_response

        site_url = "https://example.com"

        session_id, session_url = self.service.create_session(self.payment, site_url)

        self.assertEqual(session_id, "transaction_test_id")
        self.assertEqual(session_url, "https://platega.io/test-session")

        mock_httpx_post.assert_called_once()
        _, kwargs = mock_httpx_post.call_args

        self.assertIn("/transaction/process", kwargs["url"])

        self.assertEqual(kwargs["json"]["paymentMethod"], self.service.payment_method)
        self.assertEqual(
            kwargs["json"]["paymentDetails"]["amount"], float(self.payment.amount)
        )
        self.assertEqual(kwargs["json"]["paymentDetails"]["currency"], "EUR")
        self.assertEqual(kwargs["json"]["description"], self.payment.sale.lot_title)

        success_url = kwargs["json"]["return"]
        self.assertIn(f"/list/{str(self.payment.sale.lot_id)}", success_url)
        cancel_url = kwargs["json"]["failedUrl"]
        self.assertIn(f"/payment-cancel/{str(self.payment.id)}", cancel_url)

    @patch("httpx.post")
    def test_create_session_amount_exceeds_max(self, mock_httpx_post):
        """
        If payment.amount > MAX_LOT_AMOUNT, PlategaCreateSessionError should be raised.
        """
        with override_settings(MAX_LOT_AMOUNT=150):
            self.payment.amount = 151
            self.payment.save()

            with self.assertRaises(PlategaCreateSessionError) as cm:
                self.service.create_session(self.payment, "https://example.com")

            self.assertIn("greater than max lot amount", str(cm.exception))
            mock_httpx_post.assert_not_called()

    @patch("httpx.post")
    def test_create_session_http_error(self, mock_httpx_post):
        """
        If httpx raises an HTTPError, our service should raise PlategaCreateSessionError.
        """
        mock_httpx_post.side_effect = httpx.HTTPError("Something went wrong")

        with self.assertRaises(PlategaCreateSessionError) as cm:
            self.service.create_session(self.payment, "https://example.com")

        self.assertIn("Platega error during session creating", str(cm.exception))
        mock_httpx_post.assert_called_once()

    def test_get_session_status_no_session_id(self):
        """
        If payment.session_id is not set, the service should raise PaymentWithoutSessionIdError.
        """
        self.payment.session_id = None  # No session ID

        with self.assertRaises(PaymentWithoutSessionIdError):
            self.service.get_session_status(self.payment)

    @patch("httpx.get")
    def test_get_session_status_paid(self, mock_httpx_get):
        """
        If the retrieved session.status is 'CONFIRMED', return Payment.StatusChoices.PAID.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "CONFIRMED"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.PAID)

        mock_httpx_get.assert_called_once()
        _, kwargs = mock_httpx_get.call_args
        self.assertIn(str(self.payment.session_id), kwargs["url"])

    @patch("httpx.get")
    def test_get_session_status_expired(self, mock_httpx_get):
        """
        If the retrieved session.status is 'EXPIRED', return Payment.StatusChoices.EXPIRED.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "EXPIRED"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.EXPIRED)

    @patch("httpx.get")
    def test_get_session_status_canceled(self, mock_httpx_get):
        """
        If the retrieved session.status is 'CANCELED', return Payment.StatusChoices.CANCELED.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "CANCELED"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.CANCELED)

    @patch("httpx.get")
    def test_get_session_status_failed(self, mock_httpx_get):
        """
        If the retrieved session.status is 'FAILED', return Payment.StatusChoices.FAILED.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "FAILED"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.FAILED)

    @patch("httpx.get")
    def test_get_session_status_pending(self, mock_httpx_get):
        """
        If the retrieved session.status is something other than the defined statuses, return PENDING.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "PROCESSING"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.PENDING)

    @patch("httpx.get")
    def test_get_session_status_http_error(self, mock_httpx_get):
        """
        If httpx raises an HTTPError when retrieving the session, our service should raise PlategaRetrieveSessionError.
        """
        mock_httpx_get.side_effect = httpx.HTTPError("Cannot retrieve session")

        with self.assertRaises(PlategaRetrieveSessionError) as cm:
            self.service.get_session_status(self.payment)

        self.assertIn("Platega error during session status checking", str(cm.exception))
        mock_httpx_get.assert_called_once()


class SafonProcessingServiceTests(BaseTestCase):
    def setUp(self):
        """
        Set up the test case by creating a PaymentFactory instance.
        """
        self.payment = PaymentFactory()
        self.sale = SaleFactory(payment=self.payment)
        self.service = SafonProcessingService()

    def test_init_missing_settings_raises_error(self):
        """
        If any required Safon setting is not set, the service should raise ImproperlyConfigured.
        """
        for key in [
            "SAFON_CLIENT_ID",
            "SAFON_CLIENT_SECRET",
            "SAFON_BASE_URL",
            "MAX_LOT_AMOUNT",
            "SAFON_CATEGORY_ID",
        ]:
            with override_settings(**{key: None}):
                with self.assertRaises(ImproperlyConfigured):
                    SafonProcessingService()

    @patch("httpx.post")
    def test_get_access_token_success(self, mock_httpx_post):
        """
        Test successful retrieval of an access token from Safon.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "access_token": "test_token",
            "expires_in": 3600,
        }
        mock_response.raise_for_status.return_value = None

        mock_httpx_post.return_value = mock_response

        token = self.service._get_access_token()

        self.assertEqual(token, "test_token")
        mock_httpx_post.assert_called_once()
        _, kwargs = mock_httpx_post.call_args
        self.assertIn("/tokens", kwargs["url"])

    @patch("httpx.post")
    def test_get_access_token_http_error(self, mock_httpx_post):
        """
        If httpx raises an HTTPError when retrieving the access token, our service should raise SafonAuthError.
        """
        mock_httpx_post.side_effect = httpx.HTTPError("Cannot retrieve token")

        with self.assertRaises(SafonAuthError) as cm:
            self.service._get_access_token()

        self.assertIn("Safon error during authentication", str(cm.exception))
        mock_httpx_post.assert_called_once()

    def test_get_headers(self):
        """
        Test that _get_headers returns the correct headers.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        headers = self.service._get_headers()
        self.assertEqual(headers["Content-Type"], "application/json")
        self.assertNotIn("Authorization", headers)
        self.service._get_access_token.assert_not_called()

        headers = self.service._get_headers(with_auth=True)
        self.assertEqual(headers["Content-Type"], "application/json")
        self.assertEqual(headers["Authorization"], "Bearer test_token")

    @patch("httpx.post")
    def test_create_session_success(self, mock_httpx_post):
        """
        Test successful creation of a payment session.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_response = MagicMock()
        mock_response.json.return_value = {
            "session_url": "https://safon.io/session/ses-id"
        }
        mock_response.raise_for_status.return_value = None

        mock_httpx_post.return_value = mock_response

        site_url = "https://example.com"
        session_id, session_url = self.service.create_session(self.payment, site_url)

        # check that session_url localized to english
        self.assertEqual(session_url, "https://safon.io/en/session/ses-id")
        self.assertEqual(session_id, "ses-id")

        mock_httpx_post.assert_called_once()
        _, kwargs = mock_httpx_post.call_args

        self.assertEqual(kwargs["json"]["email"], self.payment.sale.buyer.email)
        self.assertEqual(kwargs["json"]["user_name"], self.payment.sale.buyer.username)
        self.assertEqual(f"{self.service.base_url}product/session", kwargs["url"])
        # check that product_name contains PRODUCT_NAME_PREFIX
        self.assertEqual(
            kwargs["json"]["product_name"],
            f"Giftlab service via {self.payment.sale.lot_title}",
        )
        self.assertEqual(kwargs["json"]["price"], math.ceil(self.payment.amount))
        self.assertEqual(kwargs["json"]["currency"], "USD")
        self.assertEqual(kwargs["json"]["category_id"], self.service.category_id)
        self.assertEqual(f"{site_url}/orders", kwargs["json"]["return_url"])
        self.assertEqual(
            f"{site_url}/api/v1/payments/update-status/{self.payment.id}/",
            kwargs["json"]["callback_url"],
        )
        self.assertEqual(
            f"{site_url}/payment-cancel/cancel", kwargs["json"]["cancel_url"]
        )

    def test_create_session_amount_exceeds_max(self):
        """
        Test that create_session raises SafonCreateSessionError if payment amount exceeds max lot amount.
        """
        self.payment.amount = Decimal("10000.00")
        self.payment.save()

        with self.assertRaises(SafonCreateSessionError) as cm:
            self.service.create_session(self.payment, "https://example.com")

        self.assertIn("Amount", str(cm.exception))
        self.assertIn("is greater than max lot amount", str(cm.exception))

    @patch("httpx.post")
    def test_create_session_http_error(self, mock_httpx_post):
        """
        Test that create_session raises SafonCreateSessionError if httpx raises an HTTPError.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_httpx_post.side_effect = httpx.HTTPError("Cannot create session")

        with self.assertRaises(SafonCreateSessionError) as cm:
            self.service.create_session(self.payment, "https://example.com")

        self.assertIn("Safon error during session creating", str(cm.exception))
        mock_httpx_post.assert_called_once()
        _, kwargs = mock_httpx_post.call_args
        self.assertIn("/product/session", kwargs["url"])

    @patch("httpx.get")
    def test_get_session_status_paid(self, mock_httpx_get):
        """
        If the retrieved session.status is 'paid', return Payment.StatusChoices.PAID.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "paid"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.PAID)

        mock_httpx_get.assert_called_once()
        _, kwargs = mock_httpx_get.call_args
        self.assertIn(str(self.payment.session_id), kwargs["url"])

    @patch("httpx.get")
    def test_get_session_status_failed(self, mock_httpx_get):
        """
        If the retrieved session.status is 'failed', return Payment.StatusChoices.FAILED.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "failed"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.FAILED)

    @patch("httpx.get")
    def test_get_session_status_pending(self, mock_httpx_get):
        """
        If the retrieved session.status is something other than the defined statuses, return PENDING.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "pending"}
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        status = self.service.get_session_status(self.payment)
        self.assertEqual(status, Payment.StatusChoices.PENDING)

    @patch("httpx.get")
    def test_get_session_status_http_error(self, mock_httpx_get):
        """
        If httpx raises an HTTPError when retrieving the session, our service should raise PlategaRetrieveSessionError.
        """
        self.service._get_access_token = MagicMock(return_value="test_token")

        mock_httpx_get.side_effect = httpx.HTTPError("Cannot retrieve session")

        with self.assertRaises(SafonRetrieveSessionError) as cm:
            self.service.get_session_status(self.payment)

        self.assertIn("Safon error during session status checking", str(cm.exception))
        mock_httpx_get.assert_called_once()
