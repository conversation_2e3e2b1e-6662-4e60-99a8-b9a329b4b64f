from unittest.mock import MagicMock, patch

import requests

from base.base_test_case import BaseTestCase
from payments.exceptions.crypto_service_exceptions import TronCryptoServiceError
from payments.services.crypto_service import TronCryptoService


class TronCryptoServiceTests(BaseTestCase):
    def setUp(self):
        self.service = TronCryptoService()
        self.test_address = "THezbnG4xKJLfVVv8kEqNZMAQeABUJkfWV"

    @patch("requests.post")
    def test_validate_address_returns_true(self, mock_post):
        """
        If the API response has "result": True,
        validate_address should return True.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"result": True}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        is_valid = self.service.validate_address(self.test_address)
        self.assertTrue(is_valid)

        mock_post.assert_called_once_with(
            "https://api.shasta.trongrid.io/wallet/validateaddress",
            json={"address": self.test_address},
        )

    @patch("requests.post")
    def test_validate_address_returns_false(self, mock_post):
        """
        If the API response has "result": False,
        validate_address should return False.
        """
        mock_response = MagicMock()
        mock_response.json.return_value = {"result": False}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        is_valid = self.service.validate_address(self.test_address)
        self.assertFalse(is_valid)

        mock_post.assert_called_once()

    @patch("requests.post")
    def test_validate_address_request_exception(self, mock_post):
        """
        If requests raises a RequestException (e.g., network error, 4xx, 5xx),
        a TronCryptoServiceError should be raised.
        """
        mock_post.side_effect = requests.exceptions.RequestException

        with self.assertRaises(TronCryptoServiceError) as ctx:
            self.service.validate_address(self.test_address)

        self.assertIn("Error validating address", str(ctx.exception))

        mock_post.assert_called_once()

    @patch("requests.post")
    def test_validate_address_http_error_raises_exception(self, mock_post):
        """
        If response.raise_for_status() raises a requests.exceptions.HTTPError,
        we should raise TronCryptoServiceError.
        """
        mock_response = MagicMock()
        # Simulate .raise_for_status() failing
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError(
            "Bad Request"
        )
        mock_post.return_value = mock_response

        with self.assertRaises(TronCryptoServiceError):
            self.service.validate_address(self.test_address)

        mock_post.assert_called_once()
