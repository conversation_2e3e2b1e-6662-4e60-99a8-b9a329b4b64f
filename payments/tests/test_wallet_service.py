from datetime import timedel<PERSON>
from decimal import Decimal

from django.utils import timezone

from base.base_test_case import BaseTestCase
from base.factories import (
    DoneWithdrawFactory,
    ErrorWithdrawFactory,
    PaymentFactory,
    PendingWithdrawFactory,
    SaleTransactionFactory,
    UserFactory,
    WithdrawTransactionFactory,
)
from payments.dtos.wallet_dto import WalletDTO
from payments.models import Payment
from payments.services.wallet_service import WalletService


class WalletServiceTests(BaseTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.wallet_service = WalletService()

    def test_get_last_user_withdraw_no_withdraws(self):
        """
        If user has no Withdraw records, get_last_user_withdraw should return None.
        """
        last_withdraw = self.wallet_service.get_last_user_withdraw(self.user)
        self.assertIsNone(last_withdraw)

    def test_get_last_user_withdraw_single(self):
        """
        If user has exactly one Withdraw, that one should be returned.
        """
        withdraw = PendingWithdrawFactory(user=self.user)

        last_withdraw = self.wallet_service.get_last_user_withdraw(self.user)
        self.assertEqual(last_withdraw, withdraw)

    def test_get_last_user_withdraw_multiple(self):
        """
        If user has multiple Withdraw records, the latest (by created_at) is returned.
        """
        DoneWithdrawFactory(user=self.user, created_at=timezone.now())
        w2 = PendingWithdrawFactory(
            user=self.user, created_at=timezone.now() + timedelta(hours=1)
        )

        last_withdraw = self.wallet_service.get_last_user_withdraw(self.user)
        self.assertEqual(last_withdraw, w2)

    def test_get_user_balance_no_transactions(self):
        """
        If user has no relevant transactions, balance should be 0.00.
        """
        balance = self.wallet_service.get_user_balance(self.user)
        self.assertEqual(balance, Decimal("0.00"))

    def test_get_user_balance_payments_paid(self):
        """
        Only transactions related to Payment with status=PAID or
        Withdraw with DONE/PENDING statuses are included in the balance.
        """

        PaymentFactory(
            status=Payment.StatusChoices.PAID,
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("150")),
        )
        PaymentFactory(
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("200"))
        )

        DoneWithdrawFactory(
            transaction=WithdrawTransactionFactory(
                user=self.user, amount=Decimal("-70")
            )
        )
        ErrorWithdrawFactory(
            transaction=WithdrawTransactionFactory(
                user=self.user, amount=Decimal("-20")
            )
        )

        balance = self.wallet_service.get_user_balance(self.user)
        self.assertEqual(balance, Decimal("80"))

    def test_get_user_balance_with_pending_withdraw(self):
        """
        A Withdraw in PENDING status is included in the user’s transaction sum.
        """
        PaymentFactory(
            status=Payment.StatusChoices.PAID,
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("40")),
        )
        PendingWithdrawFactory(
            user=self.user,
            transaction=WithdrawTransactionFactory(
                user=self.user, amount=Decimal("-40")
            ),
        )

        balance = self.wallet_service.get_user_balance(self.user)
        self.assertEqual(balance, Decimal("0"))

    def test_get_wallet_dto_empty(self):
        """
        get_wallet_dto should return a WalletDTO with balance=0.00
        and last_withdraw=None if user has no transactions or withdraws.
        """
        dto = self.wallet_service.get_wallet_dto(self.user)
        self.assertIsInstance(dto, WalletDTO)
        self.assertEqual(dto.balance, Decimal("0.00"))
        self.assertIsNone(dto.last_withdraw)

    def test_get_wallet_dto_with_data(self):
        """
        If user has relevant transactions and multiple withdraws,
        get_wallet_dto returns the correct balance and last withdraw.
        """
        PaymentFactory(
            status=Payment.StatusChoices.PAID,
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("100")),
        )
        DoneWithdrawFactory(
            user=self.user,
            transaction=WithdrawTransactionFactory(
                user=self.user, amount=Decimal("-100")
            ),
        )
        PaymentFactory(
            status=Payment.StatusChoices.PAID,
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("200")),
        )
        last_withdraw = PendingWithdrawFactory(
            user=self.user,
            transaction=WithdrawTransactionFactory(
                user=self.user, amount=Decimal("-200")
            ),
            created_at=timezone.now() + timedelta(hours=1),
        )
        PaymentFactory(
            status=Payment.StatusChoices.PAID,
            transaction=SaleTransactionFactory(user=self.user, amount=Decimal("300")),
        )

        dto = self.wallet_service.get_wallet_dto(self.user)
        self.assertEqual(dto.balance, Decimal("300"))
        self.assertEqual(dto.last_withdraw, last_withdraw)
