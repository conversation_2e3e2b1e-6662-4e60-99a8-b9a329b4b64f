from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from payments.services.processing_service import (
    IProcessingService,
    PlategaProcessingService,
    SafonProcessingService,
    StripeProcessingService,
)


def get_processing_service() -> IProcessingService:
    """
    Factory function to get the appropriate processing service based on settings.

    Returns:
        IProcessingService: The configured payment processing service
    """
    service_name = getattr(settings, "PAYMENT_PROCESSING_SERVICE", "stripe")

    match service_name:
        case "stripe":
            return StripeProcessingService()
        case "platega":
            return PlategaProcessingService()
        case "safon":
            return SafonProcessingService()
        case _:
            raise ImproperlyConfigured(
                f"Unknown payment processing service: {service_name}"
            )
