class PaymentServiceError(Exception):
    """
    Custom exception class for handling errors related to fee services.
    """

    pass


class PaymentServiceUserWithoutFeeGroupError(PaymentServiceError):
    """
    Custom exception class for handling errors when a user doesn't have a fee gtoup.
    """

    pass


class PaymentServiceMissingFeeServiceError(PaymentServiceError):
    """
    Custom exception class for handling errors when a fee service is missing.
    """

    pass


class PaymentServiceProcessingError(PaymentServiceError):
    """
    Custom exception class for handling errors when a payment processing fails.
    """

    pass
