class WithdrawServiceError(Exception):
    """
    Exception class for handling errors related to withdraw service.
    """

    pass


class WithdrawServiceInsufficientBalanceError(WithdrawServiceError):
    """
    Exception class for handling errors when a user doesn't have enough balance for a withdraw.
    """

    pass


class WithdrawServiceInvalidAddressError(WithdrawServiceError):
    """
    Exception class for handling errors when a user provides an invalid address for a withdraw.
    """

    pass


class WithdrawServicePendingWithdrawError(WithdrawServiceError):
    """
    Exception class for handling errors when a user already has a pending withdraw.
    """

    pass


class WithdrawServiceCryptoServiceError(WithdrawServiceError):
    """
    Exception class for handling errors when the crypto service is unavailable.
    """

    pass


class WithdrawServiceGroupBalanceError(WithdrawServiceError):
    """
    Exception class for handling errors when the group balance is insufficient for a group withdraw.
    """

    pass


class WithdrawServicePendingGroupWithdrawError(WithdrawServiceError):
    """
    Exception class for handling errors when a group already has a pending withdraw.
    """

    pass
