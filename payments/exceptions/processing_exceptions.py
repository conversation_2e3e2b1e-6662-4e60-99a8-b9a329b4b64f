class ProcessingError(Exception):
    """
    Exception class for processing errors.
    This exception is raised when there's an issue with processing a payment.
    """

    pass


class StripeProcessingError(ProcessingError):
    """
    Exception class for Stripe processing errors.
    This exception is raised when there's an issue with processing a Stripe payment.
    """

    pass


class StripeCreateSessionError(StripeProcessingError):
    """
    Exception class for Stripe session creation errors.
    This exception is raised when there's an issue with creating a Stripe payment session.
    """

    pass


class StripeRetrieveSessionError(StripeProcessingError):
    """
    Exception class for Stripe session retrieval errors.
    This exception is raised when there's an issue with retrieving a Stripe payment session.
    """

    pass


class PlategaProcessingError(ProcessingError):
    """
    Exception class for Platega processing errors.
    This exception is raised when there's an issue with processing a Platega payment.
    """

    pass


class PlategaCreateSessionError(PlategaProcessingError):
    """
    Exception class for Platega session creation errors.
    This exception is raised when there's an issue with creating a Platega payment session.
    """

    pass


class PlategaRetrieveSessionError(PlategaProcessingError):
    """
    Exception class for Platega session retrieval errors.
    This exception is raised when there's an issue with retrieving a Platega payment session.
    """

    pass


class SafonProcessingError(ProcessingError):
    """
    Exception class for Safon processing errors.
    This exception is raised when there's an issue with processing a Safon payment.
    """

    pass


class SafonCreateSessionError(SafonProcessingError):
    """
    Exception class for Safon session creation errors.
    This exception is raised when there's an issue with creating a Safon payment session.
    """

    pass


class SafonRetrieveSessionError(SafonProcessingError):
    """
    Exception class for Safon session retrieval errors.
    This exception is raised when there's an issue with retrieving a Safon payment session.
    """

    pass


class SafonAuthError(SafonProcessingError):
    """
    Exception class for Safon authentication errors.
    This exception is raised when there's an issue with Safon authentication.
    """

    pass
