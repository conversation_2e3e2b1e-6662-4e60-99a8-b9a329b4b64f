class PaymentTgBotServiceError(Exception):
    """
    Custom exception class for handling errors related to payment tg bot service.
    """

    pass


class PaymentTgBotServiceChatIdError(PaymentTgBotServiceError):
    """
    Custom exception class for handling errors when a user doesn't have a chat id.
    """

    pass


class PaymentTgBotServiceTransactionError(PaymentTgBotServiceError):
    """
    Custom exception class for handling errors when a payment doesn't have a transaction.
    """

    pass
