from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from base.exceptions import ServiceUnavailable
from base.generics import GenericAPIView, ListAPIView
from base.pagination import HundredMaxLimitOffsetPagination
from payments.exceptions.payment_service_exceptions import PaymentServiceError
from payments.exceptions.withdraw_service_exceptions import (
    WithdrawServiceCryptoServiceError,
    WithdrawServiceInsufficientBalanceError,
    WithdrawServiceInvalidAddressError,
    WithdrawServicePendingWithdrawError,
)
from payments.factories.payment_service_factory import get_payment_service
from payments.models import Payment, Transaction
from payments.models.withdraw import Withdraw
from payments.serializers import (
    TransactionListSerializer,
    UpdatePaymentStatusResponseSerializer,
    WalletDTODetailSerializer,
    WithdrawCreateRequestSerializer,
    WithdrawDetailSerializer,
)
from payments.services.wallet_service import WalletService
from payments.services.withdraw_service import WithdrawService


class PaymentUpdateStatusView(GenericAPIView):
    """
    API endpoint for updating payment statuses
    """

    response_serializer_class = UpdatePaymentStatusResponseSerializer
    permission_classes = (AllowAny,)

    def get(self, request, pk):
        payment = get_object_or_404(Payment, pk=pk)

        payment_service = get_payment_service()

        try:
            payment = payment_service.update_payment_status(payment)
        except PaymentServiceError:
            raise ServiceUnavailable

        return Response(
            self.get_response_serializer(payment).data,
            status=status.HTTP_200_OK,
        )


class WithdrawCreateView(GenericAPIView):
    """
    API endpoint for creating withdraws
    """

    request_serializer_class = WithdrawCreateRequestSerializer
    response_serializer_class = WithdrawDetailSerializer

    def post(self, request):
        serializer = self.get_request_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user

        withdraw_service = WithdrawService()

        try:
            withdraw = withdraw_service.create_withdraw(
                **serializer.validated_data, user=user
            )
        except WithdrawServiceCryptoServiceError:
            raise ServiceUnavailable
        except WithdrawServicePendingWithdrawError:
            raise ValidationError("Already have a pending withdrawal.")
        except WithdrawServiceInsufficientBalanceError:
            return ValidationError("Current balance is not enough for the withdrawal.")
        except WithdrawServiceInvalidAddressError:
            raise ValidationError({"address": "Invalid address."})

        return Response(
            self.get_response_serializer(withdraw).data,
            status=status.HTTP_201_CREATED,
        )


class WalletDetailView(GenericAPIView):
    """
    API endpoint for getting wallet details
    """

    response_serializer_class = WalletDTODetailSerializer

    def get(self, request):
        user = request.user

        wallet_service = WalletService()
        wallet_dto = wallet_service.get_wallet_dto(user=user)

        return Response(
            self.get_response_serializer(wallet_dto).data,
            status=status.HTTP_200_OK,
        )


class TransactionListView(ListAPIView):
    """
    API endpoint for getting transactions list
    """

    response_serializer_class = TransactionListSerializer
    pagination_class = HundredMaxLimitOffsetPagination
    filterset_fields = ("type",)

    def get_queryset(self):
        user = self.request.user

        return (
            Transaction.objects.filter(
                (
                    Q(
                        withdraw__status=Withdraw.StatusChoices.DONE,
                    )
                    | Q(payment__status=Payment.StatusChoices.PAID)
                ),
                user=user,
            )
            .select_related("withdraw", "payment", "payment__sale__buyer")
            .order_by("-created_at")
        )
