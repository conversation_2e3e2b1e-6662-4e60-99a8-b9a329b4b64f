from rest_framework import serializers

from payments.models import Payment, Transaction
from payments.models.withdraw import Withdraw
from sales.models import Sale


class UpdatePaymentStatusResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ("status",)
        read_only_fields = fields


class WithdrawCreateRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = Withdraw
        fields = ("address",)


class WithdrawDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Withdraw
        fields = ("address", "amount", "status", "created_at")
        read_only_fields = fields


class WalletDTODetailSerializer(serializers.Serializer):
    balance = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
    )
    last_withdraw = WithdrawDetailSerializer(read_only=True)


class TransactionSaleInfoSerializer(serializers.ModelSerializer):
    buyer_username = serializers.CharField(source="buyer.username", read_only=True)

    class Meta:
        model = Sale
        fields = ("buyer_username", "lot_thumbnail", "lot_title")
        read_only_fields = fields


class TransactionWithdrawInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Withdraw
        fields = ("address",)
        read_only_fields = fields


class TransactionListSerializer(serializers.ModelSerializer):
    sale_info = TransactionSaleInfoSerializer(source="payment.sale", read_only=True)
    amount = serializers.SerializerMethodField()
    withdraw_info = TransactionWithdrawInfoSerializer(source="withdraw", read_only=True)

    class Meta:
        model = Transaction
        fields = ("created_at", "amount", "type", "sale_info", "withdraw_info")
        read_only_fields = fields

    def get_amount(self, obj):
        if obj.type == Transaction.TransactionType.WITHDRAW:
            return -obj.amount

        return obj.amount
