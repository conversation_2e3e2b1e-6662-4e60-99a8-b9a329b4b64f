import logging

from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.fsm.storage.redis import RedisStorage
from config import config

logging.basicConfig(level=logging.INFO)

storage = RedisStorage.from_url(config.REDIS_STORAGE_URL)

bot = Bot(token=config.BOT_TOKEN, default=DefaultBotProperties(parse_mode="HTML"))
dp = Dispatcher(storage=storage)
