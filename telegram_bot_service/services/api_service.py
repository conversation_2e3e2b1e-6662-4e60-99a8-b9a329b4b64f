import logging

import aiohttp
from config import config
from exceptions import RemoveBlurException

logger = logging.getLogger(__name__)


class APIService:
    """
    Service for making API requests.
    """

    def __init__(
        self,
        api_key: str = config.INTERNAL_API_TOKEN,
        base_url: str = "http://api:8000/api/v1",
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.client = None

    async def __aenter__(self):
        self.client = aiohttp.ClientSession()

        return self

    async def __aexit__(self, exc_type, exc, tb):
        if self.client:
            await self.client.close()

    def _get_headers(self, headers: dict) -> dict:
        return {"X-Service-Token": f"{self.api_key}", **headers}

    async def _make_request(
        self, method: str, url: str, data: dict = None, headers: dict = None, **kwargs
    ) -> dict | None:
        headers = self._get_headers(headers or {})

        async with self.client.request(
            method, url, json=data, headers=headers, **kwargs
        ) as response:
            response.raise_for_status()

            return await response.json()

    async def lot_remove_blur(self, lot_id: str) -> None:
        try:
            await self._make_request(
                method="PATCH",
                url=f"{self.base_url}/lots/{lot_id}/remove-blur/",
            )
        except aiohttp.ClientResponseError as e:
            logger.error(f"Error removing blur from lot {lot_id}: {e}")

            raise RemoveBlurException(f"Error removing blur from lot: {e}")
