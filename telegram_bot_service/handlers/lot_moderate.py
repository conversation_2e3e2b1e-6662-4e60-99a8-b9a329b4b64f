from aiogram import F, Router
from aiogram.types.callback_query import Callback<PERSON><PERSON>y
from config import config
from exceptions import RemoveBlurException
from services.api_service import APIService

router = Router()


@router.callback_query(
    F.data.startswith("blur_"), F.message.chat.id == config.SERVICE_CHAT_ID
)
async def handle_callback(callback_query: CallbackQuery, *args, **kwargs):
    orig_reply_markup = callback_query.message.reply_markup
    message_caption = callback_query.message.caption

    await callback_query.message.delete_reply_markup()

    if callback_query.data.startswith("blur_remove_"):
        lot_id = callback_query.data.split("_")[-1]

        async with APIService() as api_service:
            try:
                await api_service.lot_remove_blur(lot_id=lot_id)

                caption = f"{message_caption}\n\nBlur removed 👎"
                await callback_query.message.edit_caption(caption=caption)
            except RemoveBlurException as err:
                await callback_query.message.answer(f"Error removing blur: {err}")
                await callback_query.message.edit_reply_markup(
                    reply_markup=orig_reply_markup
                )
    elif callback_query.data.startswith("blur_accept_"):
        caption = f"{message_caption}\n\nBlur accepted 👍"
        await callback_query.message.edit_caption(caption=caption)
