import asyncio
import logging

from aiogram.types import Bot<PERSON>ommand
from create_bot import bot, dp


async def set_commands():
    bot_commands = [
        BotCommand(command="/start", description="Start bot"),
    ]
    await bot.set_my_commands(bot_commands)


async def on_startup(dispatcher):
    import handlers  # noqa

    await set_commands()
    dispatcher.include_routers(handlers.main.router, handlers.lot_moderate.router)
    logging.info("Service BOT started")


async def main():
    dp.startup.register(on_startup)
    await bot.delete_webhook(drop_pending_updates=True)
    await dp.start_polling(bot)


if __name__ == "__main__":
    asyncio.run(main())
