from aiogram import F, Router
from aiogram.enums.chat_type import ChatType
from aiogram.filters import CommandStart
from aiogram.fsm.context import FSMContext
from aiogram.fsm.storage.base import StorageKey
from aiogram.types import Message, Reply<PERSON>eyboardRemove
from config import config
from config.buttons_texts import BUTTONS
from config.messages_texts import MESSAGES
from create_bot import dp
from keyboards.finish_conversation_kb import finish_conversation_kb
from states.request_state import Request
from utils.extract_user_id import extract_user_id

router = Router()


@router.message(CommandStart(), F.chat.type == ChatType.PRIVATE)
async def start(message: Message, *args, **kwargs):
    await message.answer(MESSAGES.get("welcome"))


@router.message(
    F.chat.type == ChatType.PRIVATE, F.text == BUTTONS.get("finish_conversation")
)
async def handle_finish_conversation(
    message: Message, state: FSMContext, *args, **kwargs
):
    await state.clear()
    await message.answer(
        MESSAGES.get("request_finished"), reply_markup=ReplyKeyboardRemove()
    )
    await message.bot.send_message(
        chat_id=config.SUPPORT_CHAT_ID,
        text=f"Conversation with @{message.from_user.username} finished.",
    )


@router.message(F.chat.type == ChatType.PRIVATE)
async def handle_request(message: Message, state: FSMContext, *args, **kwargs):
    user_info = f"User: @{message.from_user.username} {{{{{message.from_user.id}}}}}"

    if message.text:
        await message.bot.send_message(
            chat_id=config.SUPPORT_CHAT_ID, text=f"{user_info}\n\n{message.text}"
        )
    elif message.caption:
        new_caption = f"{user_info}\n\n{message.caption}"
        await message.copy_to(chat_id=config.SUPPORT_CHAT_ID, caption=new_caption)
    else:
        await message.copy_to(chat_id=config.SUPPORT_CHAT_ID, caption=user_info)

    if not (await state.get_state()):
        await message.answer(
            MESSAGES.get("request_received"), reply_markup=finish_conversation_kb()
        )
        await state.set_state(Request.request)


@router.message(F.reply_to_message, F.chat.id == config.SUPPORT_CHAT_ID)
async def handle_reply(message: Message, *args, **kwargs):
    user_id = extract_user_id(
        message.reply_to_message.text or message.reply_to_message.caption
    )

    if message.text == "/finish":
        users_state = FSMContext(
            storage=dp.storage, key=StorageKey(message.bot.id, user_id, user_id)
        )

        if await users_state.get_state():
            await users_state.clear()

            await message.bot.send_message(
                chat_id=user_id,
                text=MESSAGES.get("request_finished"),
                reply_markup=ReplyKeyboardRemove(),
            )

            return await message.answer(f"Conversation with {user_id} finished.")

        return await message.answer(f"Conversation with {user_id} is not active.")

    await message.copy_to(user_id)
    await message.answer("Message sent to the user.")
