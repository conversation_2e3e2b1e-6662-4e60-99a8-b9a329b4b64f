import os.path

from nudenet import NudeDetector
from PIL import Image, ImageFilter

nude_detector = NudeDetector()


class NSFWDetectService:
    DETECT_CLASSES = {
        "FEMALE_BREAST_EXPOSED",
        "FEMALE_GENITALIA_EXPOSED",
        "ANUS_EXPOSED",
        "MALE_GENITALIA_EXPOSED",
    }
    BLUR_SUFFIX = "blur"

    def detect(self, image_path: str) -> bool:
        """
        Detect nudity in the image

        Args:
            image_path(str): path to image

        Returns:
            bool: True if image contains nudity
        """
        results = nude_detector.detect(image_path)

        if not results:
            return False

        for result in results:
            if result["class"] in self.DETECT_CLASSES:
                return True

        return False

    def _generate_blur_image_path(self, image_path: str) -> str:
        """
        Generate blur image file path

        Args:
            image_path(str): path to image

        Returns:
            str: blur image path
        """
        current_file_name, _ = os.path.splitext(image_path)
        blur_filename = f"{current_file_name}_{self.BLUR_SUFFIX}"

        return image_path.replace(current_file_name, blur_filename)

    def blur_image(self, image_path: str, blur_image_path: str | None = None) -> str:
        """
        Blur image with pillow

        Args:
            image_path(str): path to image
            blur_image_path(str): path to save blured image

        Returns:
            str: blur image path
        """
        with Image.open(image_path) as img:
            img = img.filter(ImageFilter.GaussianBlur(radius=15))

            blur_image_path = blur_image_path or self._generate_blur_image_path(
                image_path
            )
            img.save(blur_image_path)

        return blur_image_path
