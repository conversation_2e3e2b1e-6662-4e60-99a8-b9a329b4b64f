import telebot


class BaseTelegramBotService:
    """
    Base class for Telegram bot services.
    """

    def __init__(self, token: str, parse_mode: str = "HTML"):
        self.bot = telebot.TeleBot(token=token, parse_mode=parse_mode)
        self.parse_mode = parse_mode

    def send_message(self, chat_id: int, text: str, **kwargs):
        self.bot.send_message(chat_id=chat_id, text=text, **kwargs)
