from email.mime.image import MIMEImage
from functools import cache

from django.contrib.staticfiles import finders


@cache
def logo_mime_image():
    with open(finders.find("images/giftlab_logo_145_80.png"), "rb") as f:
        logo_data = f.read()
    logo = MIMEImage(logo_data)
    logo.add_header("Content-ID", "<logo>")
    logo.add_header("Content-Disposition", "attachment", filename="logo.png")

    return logo
