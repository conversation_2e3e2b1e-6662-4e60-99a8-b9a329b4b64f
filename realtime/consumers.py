import json
import logging

from channels.generic.websocket import AsyncWebsocketConsumer

logger = logging.getLogger(__name__)


class EventsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        user = self.scope["user"]

        if not user.is_authenticated:
            logger.info("User is not authenticated")

            return

        self.group_name = f"events_{user.id}"
        await self.channel_layer.group_add(self.group_name, self.channel_name)

        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.group_name, self.channel_name)

    async def send_event(self, event: dict):
        event_type = event["event_type"]
        payload = json.loads(event["payload"])

        await self.send(
            text_data=json.dumps(
                {
                    "event_type": event_type,
                    "payload": payload,
                }
            )
        )
