import json

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from realtime.enums import EventTypeEnum


def broadcast_event(user_id: str, event_type: EventTypeEnum, payload: dict) -> None:
    """
    Broadcast event to user

    Args:
        user_id (str):
        event_type (EventTypeEnum):
        payload (dict):

    Returns:
        None

    """
    layer = get_channel_layer()
    group = f"events_{user_id}"

    async_to_sync(layer.group_send)(
        group,
        {
            "type": "send_event",
            "event_type": event_type,
            "payload": json.dumps(payload, default=str),
        },
    )
