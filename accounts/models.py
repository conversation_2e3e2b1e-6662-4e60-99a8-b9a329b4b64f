from django.conf import settings
from django.contrib.auth.base_user import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from django.contrib.auth.models import PermissionsMixin
from django.db import models
from django.db.models.functions.text import Lower
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from accounts.validators import UsernameValidator
from base.models import TimeStampedUUIDModel


class UserManager(BaseUserManager):
    """Manager for User model"""

    def _create_user(self, email, password, is_staff, is_superuser, **extra_fields):
        if not email:
            raise ValueError("Users must have an email address")

        now = timezone.now()
        email = self.normalize_email(email)
        user = self.model(
            email=email,
            is_staff=is_staff,
            is_active=True,
            is_superuser=is_superuser,
            last_login=now,
            date_joined=now,
            **extra_fields,
        )
        user.set_password(password)
        user.save(using=self._db)

        return user

    def create_user(self, email, password, **extra_fields):
        return self._create_user(email, password, False, False, **extra_fields)

    def create_superuser(self, email, password, **extra_fields):
        user = self._create_user(email, password, True, True, **extra_fields)

        return user


class FeeGroup(TimeStampedUUIDModel):
    """Database model for user's fee groups"""

    name = models.CharField(
        max_length=255,
        unique=True,
        help_text=_("Name of the fee group."),
    )
    sale_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text=_("Sale percentage for the fee group."),
    )

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} {self.sale_percent}"


class FeeGroupTgConfig(TimeStampedUUIDModel):
    """Database model for user's fee groups"""

    fee_group = models.OneToOneField(
        FeeGroup,
        on_delete=models.CASCADE,
        related_name="tg_config",
        help_text=_("Fee group associated with the tg config."),
    )
    chat_id = models.BigIntegerField(
        help_text=_("Chat id for the fee group."),
    )

    def __str__(self):
        return f"{self.fee_group.name} {self.chat_id}"


class User(AbstractBaseUser, PermissionsMixin, TimeStampedUUIDModel):
    """Database model for users in the system"""

    username_validator = UsernameValidator()

    username = models.CharField(
        _("username"),
        max_length=150,
        help_text=_("Required. 150 characters or fewer. Letters, digits and . _ only."),
        validators=[username_validator],
        error_messages={
            "unique": _("A user with that username already exists."),
        },
        db_index=True,
    )
    email = models.EmailField(
        _("email address"),
        unique=True,
        error_messages={
            "unique": _("A user with that email already exists."),
        },
        help_text=_("Required. Email address of the user."),
    )
    is_staff = models.BooleanField(
        _("staff status"),
        default=False,
        help_text=_("Designates whether the user can log into this admin site."),
    )
    is_active = models.BooleanField(
        _("active"),
        default=True,
        help_text=_(
            "Designates whether this user should be treated as active. "
            "Unselect this instead of deleting accounts."
        ),
    )
    is_confirmed = models.BooleanField(
        _("confirmed"),
        default=True,
        help_text=_("Designates whether this user has confirmed their account."),
    )
    date_joined = models.DateTimeField(_("date joined"), default=timezone.now)
    fee_group = models.ForeignKey(
        FeeGroup,
        on_delete=models.SET_NULL,
        null=True,
        related_name="users",
        help_text=_("Fee group associated with the user."),
    )

    objects = UserManager()

    EMAIL_FIELD = "email"
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username"]

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        ordering = ["-created_at"]
        constraints = [
            models.UniqueConstraint(Lower("username"), name="unique_username"),
            models.UniqueConstraint(Lower("email"), name="unique_email"),
        ]

    def clean(self):
        super().clean()
        self.email = self.__class__.objects.normalize_email(self.email)

    def __str__(self):
        return self.email


class Platform(TimeStampedUUIDModel):
    """Database model for social platforms"""

    name = models.CharField(
        max_length=255,
        unique=True,
        help_text=_("Name of the platform."),
    )
    icon = models.ImageField(
        upload_to="platforms/",
        help_text=_("Icon of the platform."),
    )
    lots_available = models.BooleanField(
        default=False,
        help_text=_("Indicates whether the platform can be used for lots."),
    )

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


class Profile(TimeStampedUUIDModel):
    """Database model for User's profile"""

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="profile",
        help_text=_("User associated with the profile."),
    )
    avatar = models.ImageField(
        upload_to="avatars/",
        help_text=_("Avatar image of the user."),
    )
    platforms = models.ManyToManyField(
        Platform,
        related_name="profiles",
        through="UserPlatform",
        through_fields=("profile", "platform"),
        help_text=_("Platforms associated with the user."),
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.email} profile"


class UserPlatform(TimeStampedUUIDModel):
    """Database model for many-to-many relation User and platform"""

    profile = models.ForeignKey(
        Profile,
        on_delete=models.CASCADE,
        related_name="user_platforms",
        help_text=_("Profile associated with the user platform."),
    )
    platform = models.ForeignKey(
        Platform,
        on_delete=models.CASCADE,
        related_name="user_platforms",
        help_text=_("Platform associated with the user platform."),
    )
    url = models.URLField(
        max_length=255,
        help_text=_("URL of the user's profile on the platform."),
    )

    class Meta:
        constraints = (
            models.UniqueConstraint(
                fields=["profile", "platform"],
                name="unique_user_platform",
            ),
        )

    def __str__(self):
        return f"{self.profile.user.email} {self.platform.name} profile"


class ReservedUsername(models.Model):
    username = models.CharField(
        max_length=150,
        unique=True,
        help_text=_("Reserved username."),
    )

    class Meta:
        ordering = ["username"]

    def __str__(self):
        return self.username
