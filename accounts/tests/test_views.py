from io import BytesIO

from django.contrib.auth import get_user_model
from django.core import mail
from django.core.files.storage import default_storage
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from PIL import Image

from base.base_test_case import BaseTestCase

User = get_user_model()


class TestUserViews(BaseTestCase):
    def setUp(self):
        self.user_data = {
            "username": "user",
            "email": "<EMAIL>",
            "password": "323jqnrgifsdg123",
        }
        self.user = User.objects.create_user(**self.user_data)

    @staticmethod
    def temporary_image():
        stream = BytesIO()
        image = Image.new("RGB", (100, 100))
        image.save(stream, format="jpeg")

        return SimpleUploadedFile(
            "file.jpg", stream.getvalue(), content_type="image/jpg"
        )

    def test_create_user(self):
        url = reverse("v1:accounts:user-list")
        valid_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "secret11223344.",
        }
        response = self.client.post(url, valid_data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["username"], valid_data["username"])
        self.assertEqual(response.data["email"], valid_data["email"])
        self.assertFalse("password" in response.data)

        while len(mail.outbox) == 0:
            pass

        self.assertEqual(len(mail.outbox), 1)

        hello_message = mail.outbox[0]
        self.assertEqual(hello_message.subject, "Welcome to testserver!")
        self.assertEqual(hello_message.to, [valid_data["email"]])

        # check username is busy
        busy_username_data = valid_data.copy()
        busy_username_data["email"] = "<EMAIL>"
        response = self.client.post(url, busy_username_data)
        self.assertEqual(response.status_code, 400)
        # username in upper
        busy_username_data["username"] = busy_username_data["username"].upper()
        response = self.client.post(url, busy_username_data)
        self.assertEqual(response.status_code, 400)

        # check email is busy
        busy_email_data = valid_data.copy()
        busy_email_data["username"] = "new_test_username"
        response = self.client.post(url, busy_email_data)
        self.assertEqual(response.status_code, 400)

    def test_check_username(self):
        invalid_usernames = [
            "",
            " ",
            "   ",
            ".",
            "_",
            "_.",
            "._",
            ".username",
            "_username",
            "username.",
            "username_",
            "бодя",
            "admin",
            "payments",
            "sales",
            "lots",
            "accounts",
            "settings",
            "orders",
            "api",
            "v1",
            "docs",
            "schema",
            "media",
            "static",
        ]

        for username in invalid_usernames:
            response = self.client.post(
                reverse("v1:accounts:user-check-username"), {"username": username}
            )
            self.assertEqual(response.status_code, 400)

        for username in [self.user.username, self.user.username.upper()]:
            response = self.client.post(
                reverse("v1:accounts:user-check-username"),
                {"username": username},
            )
            self.assertEqual(response.status_code, 200)
            self.assertFalse(response.json()["username_not_busy"])

        valid_username = "validusername"
        response = self.client.post(
            reverse("v1:accounts:user-check-username"), {"username": valid_username}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.json()["username_not_busy"])

    def test_change_password(self):
        url = reverse("v1:accounts:user-change-password", args=(self.user.username,))
        valid_data = {
            "old_password": self.user_data["password"],
            "new_password": "sdgfsdfgsdfg48254784",
            "check_password": "sdgfsdfgsdfg48254784",
        }
        response = self.client.patch(url, valid_data)
        self.assertEqual(response.status_code, 401)

        self.client.force_authenticate(self.user)
        response = self.client.patch(url, valid_data)
        self.assertEqual(response.status_code, 204)

        invalid_old_password_data = {
            "old_password": self.user_data[
                "password"
            ],  # old password now is "sdgfsdfgsdfg48254784"
            "new_password": "nВІАew_pdfghfdgh123",
            "check_password": "nВІАew_pdfghfdgh123",
        }
        response = self.client.patch(url, invalid_old_password_data)
        self.assertEqual(response.status_code, 400)

        invalid_new_password_data = {
            "old_password": "sdgfsdfgsdfg48254784",
            "new_password": ".",
            "check_password": ".",
        }
        response = self.client.patch(url, invalid_new_password_data)
        self.assertEqual(response.status_code, 400)

        invalid_new_password_the_same_as_old_data = {
            "old_password": "sdgfsdfgsdfg48254784",
            "new_password": "sdgfsdfgsdfg48254784",
            "check_password": "sdgfsdfgsdfg48254784",
        }
        response = self.client.patch(url, invalid_new_password_the_same_as_old_data)
        self.assertEqual(response.status_code, 400)

        invalid_new_password_not_equal_to_check_password_data = {
            "old_password": "sdgfsdfgsdfg48254784",
            "new_password": "sdgfsdfgsdfg48254784",
            "check_password": "sdgfsdfgsdfg48254785",
        }
        response = self.client.patch(
            url, invalid_new_password_not_equal_to_check_password_data
        )
        self.assertEqual(response.status_code, 400)

    def test_change_email(self):
        url = reverse("v1:accounts:user-change-email", args=(self.user.username,))
        valid_data = {
            "password": self.user_data["password"],
            "email": "<EMAIL>",
        }
        response = self.client.patch(url, valid_data)
        self.assertEqual(response.status_code, 401)

        self.client.force_authenticate(self.user)
        response = self.client.patch(url, valid_data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["email"], valid_data["email"])

        invalid_password_data = {
            "password": "XXXXX_password",
            "email": "<EMAIL>",
        }
        response = self.client.patch(url, invalid_password_data)
        self.assertEqual(response.status_code, 400)

        invalid_email_data = {
            "password": self.user_data["password"],
            "email": "new_email",
        }
        response = self.client.patch(url, invalid_email_data)
        self.assertEqual(response.status_code, 400)

        busy_email_data = {
            "password": self.user_data["password"],
            "email": "<EMAIL>",
        }
        User.objects.create_user(
            username="new_user",
            email=busy_email_data["email"],
            password="new_user_password213412654643654356",
        )
        response = self.client.patch(url, busy_email_data)
        self.assertEqual(response.status_code, 400)

    def test_reset_password(self):
        url = reverse("v1:accounts:reset-password")

        invalid_email_data = {
            "email": "invalid_email_format",
        }
        response = self.client.post(url, invalid_email_data)
        self.assertEqual(response.status_code, 400)

        not_exist_email_data = {
            "email": "<EMAIL>",
        }
        response = self.client.post(url, not_exist_email_data)
        self.assertEqual(response.status_code, 400)

        valid_data = {
            "email": self.user_data["email"],
        }

        response = self.client.post(url, valid_data)
        self.assertEqual(response.status_code, 200)

        while len(mail.outbox) == 0:
            pass

        self.assertEqual(len(mail.outbox), 1)

        reset_password_mail = mail.outbox[0]
        self.assertEqual(reset_password_mail.subject, "Reset Password")
        self.assertEqual(reset_password_mail.to, [self.user_data["email"]])

        valid_data_in_upper = {
            "email": self.user_data["email"].upper(),
        }
        response = self.client.post(url, valid_data_in_upper)
        self.assertEqual(response.status_code, 200)

        while len(mail.outbox) == 1:
            pass

        self.assertEqual(len(mail.outbox), 2)

    def test_change_username(self):
        self.client.force_authenticate(self.user)
        url = reverse("v1:accounts:user-change-username", args=(self.user.username,))

        same_username_data = {
            "username": self.user_data["username"],
        }
        response = self.client.patch(url, same_username_data)
        self.assertEqual(response.status_code, 400)

        new_user = User.objects.create_user(
            username="exist_user",
            email="<EMAIL>",
            password="new_user_password213412654643654356",
        )

        busy_username_data = {
            "username": new_user.username,
        }
        response = self.client.patch(url, busy_username_data)
        self.assertEqual(response.status_code, 400)

        busy_username_upper_data = {
            "username": new_user.username.upper(),
        }
        response = self.client.patch(url, busy_username_upper_data)
        self.assertEqual(response.status_code, 400)

        valid_data = {
            "username": "valid_new_username",
        }
        response = self.client.patch(url, valid_data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["username"], valid_data["username"])
        self.user.refresh_from_db()
        self.assertEqual(self.user.username, "valid_new_username")

        same_username_upper_data = {
            "username": valid_data["username"].upper(),
        }
        response = self.client.patch(url, same_username_upper_data)
        self.assertEqual(response.status_code, 200)

    def test_get_user(self):
        response = self.client.get(
            reverse("v1:accounts:user-detail", args=(self.user.username,))
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["username"], self.user.username)

        response = self.client.get(
            reverse("v1:accounts:user-detail", args=(self.user.username.upper(),))
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["username"], self.user.username)

        response = self.client.get(
            reverse("v1:accounts:user-detail", args=(self.user.username.lower(),))
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["username"], self.user.username)

    def test_change_avatar(self):
        url = reverse("v1:accounts:user-change-avatar", args=(self.user.username,))

        response = self.client.patch(url, {}, format="multipart")
        self.assertEqual(response.status_code, 401)

        self.client.force_authenticate(self.user)

        response = self.client.patch(url, {}, format="multipart")
        self.assertEqual(response.status_code, 200)
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.profile.avatar)

        old_avatar = self.user.profile.avatar.name

        temporary_image = self.temporary_image()

        response = self.client.patch(
            url, {"avatar": temporary_image}, format="multipart"
        )
        self.assertEqual(response.status_code, 200)
        self.user.refresh_from_db()
        self.assertNotEqual(old_avatar, self.user.profile.avatar.name)
        # Check that the old file has been deleted from storage
        self.assertFalse(default_storage.exists(old_avatar))

    def test_fast_registration(self):
        url = reverse("v1:accounts:user-fast-registration")
        busy_email_data = {
            "email": self.user_data["email"],
        }

        response = self.client.post(url, busy_email_data)
        self.assertEqual(response.status_code, 409)

        valid_data = {
            "email": "<EMAIL>",
        }
        response = self.client.post(url, valid_data)
        self.assertEqual(response.status_code, 201)

        self.assertIn("refresh", response.json())
        self.assertIn("access", response.json())
        self.assertTrue(User.objects.filter(email=valid_data["email"]).exists())

        while len(mail.outbox) == 0:
            pass

        registration_email = mail.outbox[-1]
        self.assertEqual(registration_email.to, [valid_data["email"]])

    def test_check_email(self):
        invalid_email = "invalid_email"
        response = self.client.post(
            reverse("v1:accounts:user-check-email"), {"email": invalid_email}
        )
        self.assertEqual(response.status_code, 400)

        exists_email = self.user_data["email"]
        response = self.client.post(
            reverse("v1:accounts:user-check-email"), {"email": exists_email}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.json()["exists"])

        exists_email_uppercase = self.user_data["email"].upper()
        response = self.client.post(
            reverse("v1:accounts:user-check-email"), {"email": exists_email_uppercase}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.json()["exists"])

        not_exists_email = "<EMAIL>"
        response = self.client.post(
            reverse("v1:accounts:user-check-email"), {"email": not_exists_email}
        )
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.json()["exists"])

        not_exists_email_upper = "<EMAIL>".upper()
        response = self.client.post(
            reverse("v1:accounts:user-check-email"), {"email": not_exists_email_upper}
        )
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.json()["exists"])
