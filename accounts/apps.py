from django.apps import AppConfig
from django.conf import settings
from django.contrib.admin.apps import AdminConfig


class AccountsConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "accounts"

    def ready(self):
        import accounts.signals  # noqa


# OTPAdminConfig
class OTPAdminConfig(AdminConfig):
    default_site = (
        "django_otp.admin.OTPAdminSite"
        if settings.OTP_ADMIN_LOGIN
        else "django.contrib.admin.AdminSite"
    )
