import logging

from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.db import transaction
from django.utils.http import urlsafe_base64_decode
from faker import Faker

from accounts.exceptions import InvalidTokenException, InvalidUidException
from accounts.models import FeeGroup
from accounts.services.email_service import EmailService

User = get_user_model()

logger = logging.getLogger(__name__)


class UserService:
    """Service for User"""

    default_group_name = "All"

    def _get_default_user_group(self) -> FeeGroup:
        return FeeGroup.objects.get(name=self.default_group_name)

    def check_username_not_busy(self, username: str) -> bool:
        return not User.objects.filter(username__iexact=username).exists()

    def create_user(
        self,
        email: str,
        username: str,
        password: str,
        site_url: str,
    ) -> User:
        with transaction.atomic():
            user = User.objects.create_user(
                email=email,
                username=username,
                password=password,
                fee_group=self._get_default_user_group(),
            )

            email_service = EmailService()
            email_service.send_hello_message(user=user, site_url=site_url)

        return user

    def _check_old_password(self, user: User, old_password: str) -> bool:
        return user.check_password(old_password)

    def change_password(
        self, user: User, old_password: str, new_password: str, **kwargs
    ) -> None:
        if not self._check_old_password(user, old_password):
            raise ValueError("old_password is not correct")

        user.set_password(new_password)
        user.save(update_fields=["password"])

    def change_email(self, user: User, email: str, password: str) -> None:
        if not self._check_old_password(user, password):
            raise ValueError("password is not correct")

        user.email = email
        user.save(update_fields=["email"])

    def reset_password(self, uid: str, token: str, new_password: str, **kwargs) -> None:
        """
        Reset a user's password given their unique identifier and a valid reset token.

        This method decodes the provided URL-safe base64 encoded user ID (``uid``),
        retrieves the corresponding user, and validates the given token. If the token
        is valid, the user's password will be updated. Otherwise, an exception is raised.

        :param uid: A URL-safe base64 encoded user ID.
        :type uid: str
        :param token: The reset token associated with the user's password reset request.
        :type token: str
        :param new_password: The new password to set for the user.
        :type new_password: str
        :raises InvalidUidException: If the provided ``uid`` is invalid or does not correspond to an existing user.
        :raises InvalidTokenException: If the provided token is invalid for the given user.
        :return: None
        :rtype: None
        """

        try:
            uid = urlsafe_base64_decode(uid).decode()
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            logger.error(f"Invalid uid for reset_password: {uid}")

            raise InvalidUidException("Invalid uid.")

        if not default_token_generator.check_token(user, token):
            logger.error(f"Invalid token for reset_password: {token}, user: {user.id}")

            raise InvalidTokenException("Invalid token.")

        user.set_password(new_password)
        user.save()

    def change_username(self, user: User, username: str) -> User:
        """
        Change user's username

        Args:
            user (User):
            username (str):

        Returns:
            User: user with updated username
        """
        user.username = username
        user.save(update_fields=["username"])

        return user

    @staticmethod
    def _generate_random_username():
        """
        Generate random username

        Returns:
            str: random username
        """
        faker = Faker()

        while True:
            username = faker.user_name()

            if User.objects.filter(username=username).exists():
                continue

            return username

    @staticmethod
    def _generate_random_password():
        """
        Generate random password

        Returns:
            str: random password
        """
        faker = Faker()

        return faker.password(
            length=10,
            special_chars=True,
            digits=True,
            upper_case=True,
            lower_case=True,
        )

    def fast_registration(
        self, email: str, site_url: str, username: str | None = None
    ) -> User:
        """
        Fast registration for user

        Args:
            email (str):
            site_url (str):
            username (str | None):

        Returns:
            User: created user
        """
        username = username or self._generate_random_username()
        password = self._generate_random_password()

        user = User.objects.create_user(
            email=email,
            username=username,
            password=password,
            fee_group=self._get_default_user_group(),
        )

        email_service = EmailService()
        email_service.send_fast_registration_info(
            user=user, site_url=site_url, password=password
        )

        return user

    @staticmethod
    def check_email_exists(email: str) -> bool:
        """
        Check that email exists
        Args:
            email:

        Returns:
            bool
        """
        return User.objects.filter(email__iexact=email).exists()
