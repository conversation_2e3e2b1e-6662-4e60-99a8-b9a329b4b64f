import random
from io import BytesIO

from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile
from django.db import transaction
from PIL import Image, ImageDraw, ImageFont, ImageOps

from accounts.models import Platform, Profile, UserPlatform

User = get_user_model()


class ProfileService:
    avatar_colors = [
        "#B621D9",
        "#05C88A",
        "#000227",
        "#121442",
        "#26DBE4",
        "#00FF08",
        "#FF7A08",
        "#FFE108",
        "#001FFF",
        "#85FF00",
    ]
    avatar_size = (260, 260)
    text_color = "white"
    font_size = 120
    font = "arial.ttf"

    def _convert_to_webp(self, image: ContentFile) -> ContentFile:
        """
        Convert image to webp format

        Args:
            image (ContentFile): image from user

        Returns:
            ContentFile: converted image
        """
        with Image.open(image) as img:
            img = ImageOps.exif_transpose(img)

            if img.mode != "RGB":
                img = img.convert("RGB")

            buffer = BytesIO()
            img.save(buffer, format="WEBP", quality=95)
            buffer.seek(0)

            return ContentFile(buffer.read(), name=image.name)

    def generate_random_avatar(self, username: str) -> ContentFile:
        first_letter = username[0].upper()
        bg_color = random.choice(self.avatar_colors)
        image_size = self.avatar_size
        text_color = self.text_color
        font_size = self.font_size
        font = self.font

        img = Image.new("RGB", image_size, color=bg_color)

        draw = ImageDraw.Draw(img)

        try:
            font = ImageFont.truetype(font, font_size)
        except IOError:
            font = ImageFont.load_default(font_size)

        text_position = (image_size[0] / 2, image_size[1] / 2)
        draw.text(text_position, first_letter, fill=text_color, font=font, anchor="mm")

        buffer = BytesIO()
        img.save(buffer, format="WEBP", quality=100)
        buffer.seek(0)

        return ContentFile(buffer.read(), name=f"{username}_avatar.webp")

    def create_profile_for_user(self, user: User) -> Profile:
        avatar = self.generate_random_avatar(user.username)

        return Profile.objects.create(user=user, avatar=avatar)

    def change_avatar(self, user: User, avatar: ContentFile = None) -> None:
        with transaction.atomic():
            if user.profile.avatar:
                user.profile.avatar.delete()

            if not avatar:
                avatar = self.generate_random_avatar(user.username)
            else:
                avatar = self._convert_to_webp(avatar)

            user.profile.avatar = avatar
            user.profile.save(update_fields=["avatar"])

        user.refresh_from_db()

    def add_user_platform(self, user: User, platform: Platform, url: str) -> None:
        UserPlatform.objects.create(profile=user.profile, platform=platform, url=url)
        user.refresh_from_db()

    def delete_user_platform(self, user: User, user_platform: UserPlatform) -> None:
        user_platform.delete()
        user.refresh_from_db()
