import logging
from urllib.parse import urljoin, urlparse

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.utils.http import urlsafe_base64_encode

from accounts.tasks import send_email_task
from base.utils import logo_mime_image
from lots.models import Lot

User = get_user_model()

logger = logging.getLogger(__name__)


class EmailService:
    from_email = settings.EMAIL_HOST_USER

    def send_email(
        self,
        mail_subject: str,
        message_body: str,
        to_emails: list[str],
        **kwargs,
    ) -> None:
        """
        Send email synchronously

        Args:
            mail_subject (str):
            message_body (str):
            to_emails (list[str]):
            **kwargs: additional arguments for EmailMessage

        Returns:
            None
        """
        message = EmailMessage(
            subject=mail_subject,
            body=message_body,
            from_email=self.from_email,
            to=to_emails,
            **kwargs,
        )

        message.content_subtype = "html"
        message.attach(logo_mime_image())

        message.send()

    def send_email_in_celery(
        self, mail_subject: str, message_body: str, to_emails: list[str], **kwargs
    ) -> None:
        """
        Send email with celery

        Args:
            mail_subject (str):
            message_body (str):
            to_emails (list[str]):
            **kwargs: additional arguments for EmailMessage

        Returns:
            None
        """

        send_email_task.delay(
            mail_subject=mail_subject,
            message_body=message_body,
            to_emails=to_emails,
            from_email=self.from_email,
            **kwargs,
        )

    @staticmethod
    def _generate_reset_password_token(user: User) -> str:
        return default_token_generator.make_token(user)

    def _generate_reset_password_link(self, user: User, site_url: str) -> str:
        uid = self._encode_uid(user)
        token = self._generate_reset_password_token(user)

        return f"{site_url}reset-password/?uid={uid}&token={token}"

    @staticmethod
    def _encode_uid(user: User):
        return urlsafe_base64_encode(str(user.pk).encode())

    @staticmethod
    def _generate_reset_password_message(
        username: str, reset_link: str, site_url: str
    ) -> str:
        context = {
            "username": username,
            "reset_link": reset_link,
            "site_url": site_url,
        }

        return render_to_string("emails/reset_password.html", context)

    @staticmethod
    def _extract_domain(url: str) -> str:
        return urlparse(url).netloc

    def send_reset_password_token(self, user: User, site_url: str) -> None:
        """
        Send a reset password token to the specified user's email address.

        This method constructs a password reset email message, including a token
        that the user can use to reset their password. It sends the email in a
        background thread to avoid blocking the main execution flow.

        :param user: An instance of the User model representing the user who requested a password reset.
        :param site_url: Current url of the website
        :type user: User
        :return: None
        """
        mail_subject = "Reset Password"

        reset_link = self._generate_reset_password_link(user, site_url)
        message_body = self._generate_reset_password_message(user, reset_link, site_url)

        self.send_email_in_celery(
            mail_subject=mail_subject,
            message_body=message_body,
            to_emails=[user.email],
        )

    @staticmethod
    def _generate_fast_registration_info_message(
        email: str,
        username: str,
        password: str,
        domain: str,
        reset_link: str,
        site_url: str,
    ) -> str:
        context = {
            "email": email,
            "username": username,
            "password": password,
            "domain": domain,
            "reset_link": reset_link,
            "site_url": site_url,
        }

        return render_to_string("emails/fast_registration.html", context)

    @staticmethod
    def _generate_hello_message(username: str, domain: str, site_url: str) -> str:
        context = {
            "username": username,
            "domain": domain,
            "site_url": site_url,
        }

        return render_to_string("emails/hello_message.html", context)

    def send_fast_registration_info(
        self, user: User, password: str, site_url: str
    ) -> None:
        """
        Send user registration information.

        Args:
            user (User): new user
            password (str):
            site_url (str):

        Returns:
            None
        """
        domain = self._extract_domain(site_url)

        mail_subject = f"Welcome to {domain}!"

        reset_link = self._generate_reset_password_link(user, site_url)
        message_body = self._generate_fast_registration_info_message(
            email=user.email,
            username=user.username,
            password=password,
            domain=domain,
            reset_link=reset_link,
            site_url=site_url,
        )

        self.send_email_in_celery(
            mail_subject=mail_subject,
            message_body=message_body,
            to_emails=[user.email],
        )

    def send_hello_message(self, user: User, site_url: str) -> None:
        """
        Send welcome message to user after registration.

        Args:
            user (User): new user
            site_url (str):

        Returns:
            None
        """
        domain = self._extract_domain(site_url)

        mail_subject = f"Welcome to {domain}!"

        message_body = self._generate_hello_message(
            username=user.username,
            domain=domain,
            site_url=site_url,
        )

        self.send_email_in_celery(
            mail_subject=mail_subject,
            message_body=message_body,
            to_emails=[user.email],
        )

    @staticmethod
    def _generate_lot_blur_message(username: str, lot_id: str, site_url: str) -> str:
        lot_url = urljoin(site_url, f"/list/{lot_id}")

        context = {
            "username": username,
            "lot_url": lot_url,
        }

        return render_to_string("emails/lot_blur_message.html", context)

    def send_lot_blur_notification(self, lot: Lot, site_url: str) -> None:
        mail_subject = "Your lot is blured"

        message_body = self._generate_lot_blur_message(
            username=lot.user.username,
            lot_id=str(lot.id),
            site_url=site_url,
        )

        self.send_email(
            mail_subject=mail_subject,
            message_body=message_body,
            to_emails=[lot.user.email],
        )
