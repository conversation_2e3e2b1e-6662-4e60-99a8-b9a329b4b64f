from django.core import validators
from django.core.exceptions import ValidationError
from django.utils.deconstruct import deconstructible
from django.utils.translation import gettext_lazy as _


@deconstructible
class UsernameValidator(validators.RegexValidator):
    regex = r"^(?![._])[A-Za-z0-9._]+(?<![._])$"
    message = _(
        "Enter a valid username. This value may contain only latin letters, "
        "numbers, and . _ characters."
    )
    flags = 0

    def __call__(self, value):
        from accounts.services.reserved_username_service import ReservedUsernameService

        super().__call__(value)

        if ReservedUsernameService.check_username_is_reserved(value):
            raise ValidationError(
                "This username is invalid.", code=self.code, params={"value": value}
            )
