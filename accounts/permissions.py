from profile import Profile

from django.contrib.auth import get_user_model
from django.views import View
from rest_framework.permissions import BasePermission
from rest_framework.request import Request

User = get_user_model()


class IsSelf(BasePermission):
    def has_permission(self, request: Request, view: View):
        return bool(request.user and request.user.is_authenticated)

    def has_object_permission(self, request: Request, view: View, obj: User | Profile):
        return bool(
            request.user and request.user == obj
            if isinstance(obj, User)
            else request.user.profile == obj
        )
