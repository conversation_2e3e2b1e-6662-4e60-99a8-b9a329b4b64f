from typing import Type

from django.contrib.auth import get_user_model
from django.db.models import Model
from django.db.models.signals import post_save
from django.dispatch import receiver

from accounts.services.profile_service import ProfileService

User = get_user_model()


@receiver(post_save, sender=User)
def create_profile(
    sender: Type[Model], instance: User, created: bool, **kwargs
) -> None:
    if created:
        profile_service = ProfileService()
        profile_service.create_profile_for_user(instance)
