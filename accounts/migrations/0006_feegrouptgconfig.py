# Generated by Django 5.1.3 on 2025-06-24 13:16

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0005_reservedusername"),
    ]

    operations = [
        migrations.CreateModel(
            name="FeeGroupTgConfig",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "chat_id",
                    models.BigIntegerField(help_text="Chat id for the fee group."),
                ),
                (
                    "fee_group",
                    models.OneToOneField(
                        help_text="Fee group associated with the tg config.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tg_config",
                        to="accounts.feegroup",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
