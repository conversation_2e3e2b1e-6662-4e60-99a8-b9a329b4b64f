# Generated by Django 5.1.3 on 2025-06-24 09:42

from django.db import migrations, models

initial_usernames = [
    "admin",
    "payments",
    "sales",
    "lots",
    "accounts",
    "settings",
    "orders",
    "api",
    "v1",
    "docs",
    "schema",
    "media",
    "static",
    "payment-cancel",
    "payment-success",
    "special",
]


def create_reserved_usernames(apps, schema_editor):
    ReservedUsername = apps.get_model("accounts", "ReservedUsername")

    ReservedUsername.objects.bulk_create(
        [ReservedUsername(username=username) for username in initial_usernames]
    )


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0004_user_unique_email"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReservedUsername",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "username",
                    models.<PERSON>r<PERSON><PERSON>(
                        help_text="Reserved username.", max_length=150, unique=True
                    ),
                ),
            ],
            options={
                "ordering": ["username"],
            },
        ),
        migrations.RunPython(create_reserved_usernames, migrations.RunPython.noop),
    ]
