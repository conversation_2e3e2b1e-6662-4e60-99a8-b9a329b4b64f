# Generated by Django 5.1.3 on 2025-02-17 16:45

import django.db.models.functions.text
from django.db import migrations, models

import accounts.validators


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0002_add_all_fee_group"),
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="username",
            field=models.CharField(
                db_index=True,
                error_messages={"unique": "A user with that username already exists."},
                help_text="Required. 150 characters or fewer. Letters, digits and . _ only.",
                max_length=150,
                validators=[accounts.validators.UsernameValidator()],
                verbose_name="username",
            ),
        ),
        migrations.AddConstraint(
            model_name="user",
            constraint=models.UniqueConstraint(
                django.db.models.functions.text.Lower("username"),
                name="unique_username",
            ),
        ),
    ]
