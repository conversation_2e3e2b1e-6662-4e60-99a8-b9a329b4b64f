# Generated by Django 5.1.3 on 2024-12-18 18:17

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import accounts.validators


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="FeeGroup",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the fee group.", max_length=255, unique=True
                    ),
                ),
                (
                    "sale_percent",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Sale percentage for the fee group.",
                        max_digits=5,
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Platform",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the platform.", max_length=255, unique=True
                    ),
                ),
                (
                    "icon",
                    models.ImageField(
                        help_text="Icon of the platform.", upload_to="platforms/"
                    ),
                ),
                (
                    "lots_available",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates whether the platform can be used for lots.",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        db_index=True,
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and . _ only.",
                        max_length=150,
                        unique=True,
                        validators=[accounts.validators.UsernameValidator()],
                        verbose_name="username",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        error_messages={
                            "unique": "A user with that email already exists."
                        },
                        help_text="Required. Email address of the user.",
                        max_length=254,
                        unique=True,
                        verbose_name="email address",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "is_confirmed",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user has confirmed their account.",
                        verbose_name="confirmed",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
                (
                    "fee_group",
                    models.ForeignKey(
                        help_text="Fee group associated with the user.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users",
                        to="accounts.feegroup",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Profile",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        help_text="Avatar image of the user.", upload_to="avatars/"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="User associated with the profile.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserPlatform",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, editable=False
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "url",
                    models.URLField(
                        help_text="URL of the user's profile on the platform.",
                        max_length=255,
                    ),
                ),
                (
                    "platform",
                    models.ForeignKey(
                        help_text="Platform associated with the user platform.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_platforms",
                        to="accounts.platform",
                    ),
                ),
                (
                    "profile",
                    models.ForeignKey(
                        help_text="Profile associated with the user platform.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_platforms",
                        to="accounts.profile",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="profile",
            name="platforms",
            field=models.ManyToManyField(
                help_text="Platforms associated with the user.",
                related_name="profiles",
                through="accounts.UserPlatform",
                to="accounts.platform",
            ),
        ),
        migrations.AddConstraint(
            model_name="userplatform",
            constraint=models.UniqueConstraint(
                fields=("profile", "platform"), name="unique_user_platform"
            ),
        ),
    ]
