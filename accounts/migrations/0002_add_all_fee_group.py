# Generated by Django 5.1.3 on 2024-12-08 13:05

from django.db import migrations


def create_all_fee_group(apps, schema_editor):
    name = "All"
    sale_percent = 10

    FeeGroup = apps.get_model("accounts", "FeeGroup")

    FeeGroup.objects.get_or_create(name=name, sale_percent=sale_percent)


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [migrations.RunPython(create_all_fee_group, migrations.RunPython.noop)]
