import logging

from celery import shared_task
from django.core.mail.message import EmailMessage

from base.utils import logo_mime_image

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_email_task(
    self,
    mail_subject: str,
    message_body: str,
    to_emails: list[str],
    from_email: str,
    **kwargs,
):
    try:
        message = EmailMessage(
            subject=mail_subject,
            body=message_body,
            from_email=from_email,
            to=to_emails,
            **kwargs,
        )

        message.content_subtype = "html"
        message.attach(logo_mime_image())
        message.send()
        logger.info(f"Email sent to {to_emails}")
    except Exception as exc:
        logger.exception("Failed to send email")

        raise self.retry(exc=exc)

    return f"Email sent to {to_emails}"
