from django.contrib.auth import get_user_model, login
from drf_spectacular.utils import OpenApiResponse, extend_schema
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from accounts.exceptions import ResetPasswordException
from accounts.models import Platform
from accounts.permissions import IsSelf
from accounts.serializers import (
    CheckEmailRequestSerializer,
    CheckEmailResponseSerializer,
    CheckUsernameResponseSerializer,
    CheckUsernameSerializer,
    FastRegistrationRequestSerializer,
    FastRegistrationResponseSerializer,
    PasswordResetConfirmSerializer,
    PlatformListSerializer,
    ResetPasswordResponseSerializer,
    ResetPasswordSerializer,
    UserChangeAvatarSerializer,
    UserChangeEmailSerializer,
    UserChangePassword,
    UserChangeUsernameRequestSerializer,
    UserCreateSerializer,
    UserDetailSerializer,
    UserMeSerializer,
    UserPlatformDeleteSerializer,
    UserPlatformSerializer,
)
from accounts.services.email_service import EmailService
from accounts.services.profile_service import ProfileService
from accounts.services.user_service import UserService
from base.generics import GenericAPIView
from base.mixins import CreateModelMixin, ListModelMixin, RetrieveModelMixin
from base.viewsets import GenericViewSet

User = get_user_model()


class UserViewSet(RetrieveModelMixin, CreateModelMixin, GenericViewSet):
    """
    API endpoint for User model
    """

    lookup_value_regex = r"[\w.]+"
    lookup_field = "username__iexact"
    queryset = User.objects.select_related("profile")
    request_action_serializer_classes = {
        "create": UserCreateSerializer,
        "check_username": CheckUsernameSerializer,
        "change_email": UserChangeEmailSerializer,
        "change_password": UserChangePassword,
        "change_avatar": UserChangeAvatarSerializer,
        "add_platform": UserPlatformSerializer,
        "delete_platform": UserPlatformDeleteSerializer,
        "me": None,
        "change_username": UserChangeUsernameRequestSerializer,
        "fast_registration": FastRegistrationRequestSerializer,
        "check_email": CheckEmailRequestSerializer,
    }
    response_action_serializer_classes = {
        "create": UserMeSerializer,
        "check_username": CheckUsernameResponseSerializer,
        "change_email": UserMeSerializer,
        "change_password": None,
        "change_avatar": UserMeSerializer,
        "add_platform": UserMeSerializer,
        "delete_platform": UserMeSerializer,
        "me": UserMeSerializer,
        "retrieve": UserDetailSerializer,
        "change_username": UserMeSerializer,
        "fast_registration": FastRegistrationResponseSerializer,
        "check_email": CheckEmailResponseSerializer,
    }
    action_permission_classes = {
        "create": [AllowAny],
        "check_username": [AllowAny],
        "change_password": [IsSelf],
        "change_email": [IsSelf],
        "change_avatar": [IsSelf],
        "add_platform": [IsSelf],
        "delete_platform": [IsSelf],
        "me": [IsAuthenticated],
        "retrieve": [AllowAny],
        "change_username": [IsSelf],
        "fast_registration": [AllowAny],
        "check_email": [AllowAny],
    }

    def perform_create(self, serializer):
        site_url = self.request.build_absolute_uri("/")

        user_service = UserService()
        user = user_service.create_user(**serializer.validated_data, site_url=site_url)

        if user:
            # add session to user
            login(self.request, user)

        return user

    @action(
        detail=False,
        methods=["POST"],
        url_path="check-username",
        url_name="check-username",
    )
    def check_username(self, request, *args, **kwargs):
        """
        Check if username is busy
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        user_service = UserService()
        username_not_busy = user_service.check_username_not_busy(
            **request_serializer.validated_data
        )
        response_serializer = self.get_response_serializer(
            data={"username_not_busy": username_not_busy}
        )
        response_serializer.is_valid(raise_exception=True)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=True,
        methods=["PATCH"],
        url_path="change-username",
        url_name="change-username",
    )
    def change_username(self, request, *args, **kwargs):
        """
        Change username for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        user_service = UserService()
        user = user_service.change_username(
            user=request.user, **request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: None})
    @action(
        detail=True,
        methods=["PATCH"],
        url_path="change-password",
        url_name="change-password",
    )
    def change_password(self, request, *args, **kwargs):
        """
        Change password for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        user_service = UserService()
        user_service.change_password(
            user=request.user, **request_serializer.validated_data
        )

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(
        detail=True, methods=["PATCH"], url_path="change-email", url_name="change-email"
    )
    def change_email(self, request, *args, **kwargs):
        """
        Change email for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        user_service = UserService()
        user_service.change_email(
            user=request.user, **request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(request.user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=True,
        methods=["PATCH"],
        url_path="change-avatar",
        url_name="change-avatar",
    )
    def change_avatar(self, request, *args, **kwargs):
        """
        Change avatar for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        profile_service = ProfileService()
        profile_service.change_avatar(
            user=request.user, **request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(request.user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=True, methods=["PATCH"], url_path="add-platform", url_name="add-platform"
    )
    def add_platform(self, request, *args, **kwargs):
        """
        Add platform for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        profile_service = ProfileService()
        profile_service.add_user_platform(
            user=request.user, **request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(request.user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=True,
        methods=["PATCH"],
        url_path="delete-platform",
        url_name="delete-platform",
    )
    def delete_platform(self, request, *args, **kwargs):
        """
        Delete platform for current user
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        profile_service = ProfileService()
        profile_service.delete_user_platform(
            user=request.user, **request_serializer.validated_data
        )

        response_serializer = self.get_response_serializer(request.user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["GET"], url_path="me", url_name="me")
    def me(self, request, *args, **kwargs):
        """
        Get current user
        """
        response_serializer = self.get_response_serializer(request.user)

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        responses={
            status.HTTP_201_CREATED: FastRegistrationResponseSerializer,
            status.HTTP_400_BAD_REQUEST: OpenApiResponse(
                description="Bad request (something invalid)"
            ),
            status.HTTP_409_CONFLICT: OpenApiResponse(
                description="User with this email already exists"
            ),
        }
    )
    @action(
        detail=False,
        methods=["POST"],
        url_path="fast-registration",
        url_name="fast-registration",
    )
    def fast_registration(self, request, *args, **kwargs):
        """
        Fast registration for user who wants to make a purchase
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        email = request_serializer.validated_data["email"]
        username = request_serializer.validated_data.get("username")
        site_url = self.request.build_absolute_uri("/")

        user_service = UserService()
        user = user_service.fast_registration(
            email=email, site_url=site_url, username=username
        )

        refresh = RefreshToken.for_user(user)

        # add user session
        login(request, user)

        response_serializer = self.get_response_serializer(
            data={
                "refresh": str(refresh),
                "access": str(refresh.access_token),
            }
        )
        response_serializer.is_valid()

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @action(
        detail=False,
        methods=["POST"],
        url_path="check-email",
        url_name="check-email",
    )
    def check_email(self, request, *args, **kwargs):
        """
        Check if email exists
        """
        request_serializer = self.get_request_serializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        user_service = UserService()
        email_exists = user_service.check_email_exists(
            **request_serializer.validated_data
        )
        response_serializer = self.get_response_serializer(
            data={"exists": email_exists}
        )
        response_serializer.is_valid(raise_exception=True)

        return Response(response_serializer.data, status=status.HTTP_200_OK)


class PlatformViewSet(ListModelMixin, GenericViewSet):
    """
    API endpoint for Platform model
    """

    queryset = Platform.objects.all()
    action_permission_classes = {
        "list": [AllowAny],
    }
    response_action_serializer_classes = {
        "list": PlatformListSerializer,
    }


class ResetPasswordView(GenericAPIView):
    """
    API endpoint for resetting password
    """

    request_serializer_class = ResetPasswordSerializer
    response_serializer_class = ResetPasswordResponseSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_request_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data["user"]
        site_url = self.request.build_absolute_uri("/")

        email_service = EmailService()
        email_service.send_reset_password_token(user=user, site_url=site_url)

        return Response(
            {"detail": "A password reset link has been sent."},
            status=status.HTTP_200_OK,
        )


class ResetPasswordConfirmView(GenericAPIView):
    """
    API endpoint for resetting password confirmation
    """

    request_serializer_class = PasswordResetConfirmSerializer
    response_serializer_class = ResetPasswordResponseSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_request_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_service = UserService()

        try:
            user_service.reset_password(**serializer.validated_data)
        except ResetPasswordException:
            return Response(
                {"detail": "Invalid uid or token."}, status=status.HTTP_400_BAD_REQUEST
            )

        return Response(
            {"detail": "Password has been reset successfully."},
            status=status.HTTP_200_OK,
        )


class TokenObtainPairViewWithSession(TokenObtainPairView):
    """
    Takes a set of user credentials and returns an access and refresh JSON web
    token pair to prove the authentication of those credentials.
    """

    _serializer_class = api_settings.TOKEN_OBTAIN_SERIALIZER

    def post(self, request: Request, *args, **kwargs) -> Response:
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0])

        # add user session
        login(request, serializer.user)

        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class TokenRefreshViewWithSession(TokenRefreshView):
    """
    Takes a refresh type JSON web token and returns an access type JSON web
    token if the refresh token is valid.
    """

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0])

        refresh_token = serializer.validated_data["refresh"]
        try:
            token = RefreshToken(refresh_token)
            user = JWTAuthentication().get_user(token)
        except Exception:
            user = None

        # add user session
        if user and user.is_authenticated:
            login(request, user)

        return Response(serializer.validated_data, status=status.HTTP_200_OK)
