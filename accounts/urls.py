from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from accounts.views import (
    PlatformViewSet,
    ResetPasswordConfirmView,
    ResetPasswordView,
    TokenObtainPairViewWithSession,
    TokenRefreshViewWithSession,
    UserViewSet,
)

app_name = "accounts"

router = DefaultRouter()
router.register("users", UserViewSet, basename="user")
router.register("platforms", PlatformViewSet, basename="platform")

urlpatterns = [
    path("token/", TokenObtainPairViewWithSession.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshViewWithSession.as_view(), name="token_refresh"),
    # adding router urls
    path("", include(router.urls)),
    # reset password
    path("reset-password/", ResetPasswordView.as_view(), name="reset-password"),
    path(
        "reset-password/confirm/",
        ResetPasswordConfirmView.as_view(),
        name="reset-password-confirm",
    ),
]
