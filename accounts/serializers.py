from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from pillow_heif import HeifImagePlugin  # noqa
from rest_framework import serializers, validators
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from accounts.models import Platform, Profile, UserPlatform
from base.exceptions import ConflictError

User = get_user_model()


class CheckUsernameSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("username",)


class CheckUsernameResponseSerializer(serializers.Serializer):
    username_not_busy = serializers.BooleanField()


class UserChangeUsernameRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("username",)

    def validate_username(self, value: str) -> str:
        user = self.context["request"].user

        if user.username == value:
            raise serializers.ValidationError(
                _("New username the same as current username.")
            )

        if User.objects.filter(username__iexact=value).exclude(id=user.id).exists():
            raise serializers.ValidationError(_("Username already exists."))

        return value


class UserPlatformSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="platform.name", read_only=True)
    icon = serializers.ImageField(source="platform.icon", read_only=True)

    class Meta:
        model = UserPlatform
        fields = ("id", "platform", "url", "name", "icon")

    def validate_platform(self, value):
        user = self.context["request"].user

        if UserPlatform.objects.filter(profile=user.profile, platform=value).exists():
            raise serializers.ValidationError(_("Platform already exists."))

        return value


class PlatformListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Platform
        fields = ("id", "name", "icon", "lots_available")
        read_only_fields = fields


class UserPlatformDeleteSerializer(serializers.Serializer):
    user_platform = serializers.PrimaryKeyRelatedField(
        queryset=UserPlatform.objects.all(), write_only=True, required=True
    )


class ProfileSerializer(serializers.ModelSerializer):
    user_platforms = UserPlatformSerializer(many=True, read_only=True)

    class Meta:
        model = Profile
        fields = ("avatar", "user_platforms")
        read_only_fields = fields


class UserDetailSerializer(serializers.ModelSerializer):
    profile = ProfileSerializer(read_only=True)

    class Meta:
        model = User
        fields = (
            "username",
            "profile",
            "is_confirmed",
        )
        read_only_fields = fields


class UserMeSerializer(serializers.ModelSerializer):
    profile = ProfileSerializer(read_only=True)

    class Meta:
        model = User
        fields = (
            "email",
            "username",
            "profile",
            "is_confirmed",
        )
        read_only_fields = fields


class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = (
            "email",
            "username",
            "password",
        )

    @staticmethod
    def validate_password(value: str) -> str:
        validate_password(value)

        return value

    @staticmethod
    def validate_username(value: str) -> str:
        if User.objects.filter(username__iexact=value).exists():
            raise serializers.ValidationError(_("Username already exists."))

        return value


class UserChangePassword(serializers.Serializer):
    old_password = serializers.CharField(max_length=128, write_only=True, required=True)
    new_password = serializers.CharField(max_length=128, write_only=True, required=True)
    check_password = serializers.CharField(
        max_length=128, write_only=True, required=True
    )

    def validate_old_password(self, value: str) -> str:
        user = self.context["request"].user

        if not user.check_password(value):
            raise serializers.ValidationError(
                _("Your old password was entered incorrectly. Please enter it again.")
            )

        return value

    def validate_new_password(self, value: str) -> str:
        user = self.context["request"].user
        validate_password(value, user)

        return value

    def validate(self, attrs: dict) -> dict:
        if attrs["old_password"] == attrs["new_password"]:
            raise serializers.ValidationError(
                {"new_password": _("New password the same as old password.")}
            )

        if attrs["new_password"] != attrs["check_password"]:
            raise serializers.ValidationError(
                {"check_password": _("Passwords do not match.")}
            )

        return attrs


class UserChangeEmailSerializer(serializers.Serializer):
    password = serializers.CharField(max_length=128, write_only=True, required=True)
    email = serializers.EmailField(
        required=True,
        validators=[
            validators.UniqueValidator(
                queryset=User.objects.all(),
                message=_("A user with that email already exists."),
            )
        ],
    )

    def validate_password(self, value: str) -> str:
        user = self.context["request"].user

        if not user.check_password(value):
            raise serializers.ValidationError(
                _("Your old password was entered incorrectly. Please enter it again.")
            )

        return value


class UserChangeAvatarSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ("avatar",)
        extra_kwargs = {"avatar": {"required": False}}


class ResetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate(self, attrs: dict) -> dict:
        user = User.objects.filter(email__iexact=attrs["email"]).first()

        if not user:
            raise serializers.ValidationError(_("User with this email does not exist."))

        attrs["user"] = user

        return attrs


class PasswordResetConfirmSerializer(serializers.Serializer):
    uid = serializers.CharField(required=True)
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(max_length=128, write_only=True, required=True)
    check_password = serializers.CharField(
        max_length=128, write_only=True, required=True
    )

    def validate(self, attrs: dict) -> dict:
        new_password = attrs["new_password"]
        check_password = attrs["check_password"]

        if new_password != check_password:
            raise serializers.ValidationError(_("Passwords do not match."))

        validate_password(new_password)

        return attrs


class ResetPasswordResponseSerializer(serializers.Serializer):
    detail = serializers.CharField()


class EmailTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Add email validation logic for simplejwt's TokenObtainPairSerializer
    """

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        if self.username_field == "email":
            self.fields[self.username_field] = serializers.EmailField(write_only=True)


class PureUserSerializer(serializers.ModelSerializer):
    avatar = serializers.ImageField(source="profile.avatar", read_only=True)

    class Meta:
        model = User
        fields = ("username", "avatar")
        read_only_fields = fields


class FastRegistrationRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    username = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs: dict) -> dict:
        if User.objects.filter(email=attrs["email"]).exists():
            raise ConflictError(
                _("A user with that email already exists."),
                code="already_exists",
            )

        if username := attrs.get("username"):
            if User.objects.filter(username__iexact=username).exists():
                raise ConflictError(
                    _("A user with that username already exists."),
                    code="already_exists",
                )

        return attrs


class FastRegistrationResponseSerializer(serializers.Serializer):
    refresh = serializers.CharField()
    access = serializers.CharField()


class CheckEmailRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)


class CheckEmailResponseSerializer(serializers.Serializer):
    exists = serializers.BooleanField()
