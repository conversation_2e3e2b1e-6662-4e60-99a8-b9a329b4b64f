stages:
  - lint_and_test
  - build_dev
  - deploy_dev
  - build_prod
  - deploy_prod

.ssh_setup:
  before_script:
    - echo "Setting up SSH..."
    - 'command -v ssh-agent >/dev/null || ( apk add --update openssh )'
    - eval $(ssh-agent -s)
    - echo "$SSH_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -p $SSH_PORT $HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts

lint_and_test_merge_requests:
  image: python:3.11
  variables:
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    POSTGRES_DB: postgres
    POSTGRES_HOST_AUTH_METHOD: trust
  services:
    - name: postgres:15
      alias: db
  stage: lint_and_test
  only:
    - merge_requests
    - dev
    - main
  before_script:
    - echo "Setting up environment..."
    - cp "$DEV_ENV_FILE" .env
    - sed -i "s/^POSTGRES_USER=.*/POSTGRES_USER=$POSTGRES_USER/" .env || echo "POSTGRES_USER=$POSTGRES_USER" >> .env
    - sed -i "s/^POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=$POSTGRES_PASSWORD/" .env || echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" >> .env
    - sed -i "s/^POSTGRES_DB=.*/POSTGRES_DB=$POSTGRES_DB/" .env || echo "POSTGRES_DB=$POSTGRES_DB" >> .env
    - sed -i "s/^POSTGRES_HOST=.*/POSTGRES_HOST=db/" .env || echo "POSTGRES_HOST=db" >> .env
    - sed -i "s/^POSTGRES_PORT=.*/POSTGRES_PORT=5432/" .env || echo "POSTGRES_PORT=5432" >> .env
  script:
    - echo "Installing dependencies for lint/test..."
    - make install-dependencies
    - echo "Running Ruff linter..."
    - make ruff
    - echo "Running Django tests..."
    - make test
  coverage: '/TOTAL.*\s+(\d+%)$/'

.build:
  image: docker:23.0.0
  services:
    - docker:dind
  script:
    - echo "Logging in to Docker Hub..."
    - echo "$DOCKERHUB_PASSWORD" | docker login -u $DOCKERHUB_USERNAME --password-stdin
    - echo "Building Docker image $DOCKERHUB_REPO:$CI_COMMIT_SHA"
    - docker build -t "$DOCKERHUB_REPO:$CI_COMMIT_SHA" .
    - echo "Pushing Docker image to Docker Hub..."
    - docker push "$DOCKERHUB_REPO:$CI_COMMIT_SHA"
  after_script:
    - docker logout

.deploy:
  extends: .ssh_setup
  variables:
    DOCKER_IMAGE: "$DOCKERHUB_REPO:$CI_COMMIT_SHA"
  script:
    - echo "Pulling latest code on branch $BRANCH..."
    - ssh  -p "$SSH_PORT" "$USER@$HOST" "cd $PROJECT_ROOT && git checkout $BRANCH && git pull origin $BRANCH"
#    - echo "Add DOCKER_IMAGE to $ENV_FILE locally"
#    - echo "" >> "$ENV_FILE"
#    - echo "DOCKER_IMAGE=$DOCKER_IMAGE" >> "$ENV_FILE"
    - echo "Copy $ENV_FILE to the server via SSH"
    - cat "$ENV_FILE" | base64 -w0 | ssh  -p "$SSH_PORT" "$USER@$HOST" "base64 -d > $PROJECT_ROOT/.env && chmod 600 $PROJECT_ROOT/.env"
#    - echo "Stopping old containers..."
#    - ssh  -p "$SSH_PORT" "$USER@$HOST" "cd $PROJECT_ROOT && docker compose -f $DOCKER_COMPOSE_FILE down"
    - echo "Starting containers with new image..."
    - ssh  -p "$SSH_PORT" "$USER@$HOST" "cd $PROJECT_ROOT && echo '$DOCKERHUB_PASSWORD' | docker login -u $DOCKERHUB_USERNAME --password-stdin && docker pull $DOCKER_IMAGE && DOCKER_IMAGE=$DOCKER_IMAGE docker compose -f $DOCKER_COMPOSE_FILE up -d --build"
    - echo "Pruning old containers/images..."
    - ssh  -p "$SSH_PORT" "$USER@$HOST" "docker system prune -a -f --volumes"

build_dev:
  extends: .build
  stage: build_dev
  only:
    - dev
  variables:
    ENV_FILE: "$DEV_ENV_FILE"
    SSH_KEY: "$DEV_SSH_KEY"
    USER: "$DEV_USER"
    HOST: "$DEV_HOST"

deploy_dev:
  extends: .deploy
  stage: deploy_dev
  only:
    - dev
  when: manual
  variables:
    ENV_FILE: "$DEV_ENV_FILE"
    SSH_KEY: "$DEV_SSH_KEY"
    USER: "$DEV_USER"
    HOST: "$DEV_HOST"
    BRANCH: "dev"
    DOCKER_COMPOSE_FILE: "docker-compose.dev.yml"
    PROJECT_ROOT: "/home/<USER>/wish_backend"
    SSH_PORT: "$DEV_SSH_PORT"

build_prod:
  extends: .build
  stage: build_prod
  only:
    - main
  variables:
    ENV_FILE: "$PROD_ENV_FILE"
    SSH_KEY: "$PROD_SSH_KEY"
    USER: "$PROD_USER"
    HOST: "$PROD_HOST"

deploy_prod:
  extends: .deploy
  stage: deploy_prod
  only:
    - main
  when: manual
  variables:
    ENV_FILE: "$PROD_ENV_FILE"
    SSH_KEY: "$PROD_SSH_KEY"
    USER: "$PROD_USER"
    HOST: "$PROD_HOST"
    BRANCH: "main"
    DOCKER_COMPOSE_FILE: "docker-compose.prod.yml"
    PROJECT_ROOT: "/home/<USER>/giftlab_backend"
    SSH_PORT: "$PROD_SSH_PORT"
