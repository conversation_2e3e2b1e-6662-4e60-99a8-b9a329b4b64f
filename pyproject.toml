[project]
name = "wish-api"
version = "0.1.0"
description = "REST API"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiogram>=3.20.0",
    "aiohttp>=3.11.18",
    "amqp==5.3.1",
    "anyio==4.6.2.post1",
    "asgiref==3.8.1",
    "async-timeout==5.0.1",
    "attrs==24.2.0",
    "billiard==4.2.1",
    "celery==5.4.0",
    "celery-singleton==0.3.1",
    "certifi==2024.8.30",
    "channels-redis>=4.2.1",
    "channels[daphne]>=4.2.2",
    "charset-normalizer==3.4.0",
    "click==8.1.8",
    "click-didyoumean==0.3.1",
    "click-plugins==1.1.1",
    "click-repl==0.3.0",
    "colorama==0.4.6",
    "coverage>=7.8.0",
    "cron-descriptor==1.4.5",
    "django==5.1.3",
    "django-admin-honeypot-advanced>=1.0.1",
    "django-celery-beat==2.7.0",
    "django-celery-results==2.5.1",
    "django-cors-headers==4.6.0",
    "django-debug-toolbar==4.4.6",
    "django-filter==24.3",
    "django-modeladmin-reorder==0.3.1",
    "django-otp==1.5.4",
    "django-prometheus>=2.3.1",
    "django-redis==5.4.0",
    "django-stubs==5.1.1",
    "django-stubs-ext==5.1.1",
    "django-timezone-field==7.1",
    "djangorestframework==3.15.2",
    "djangorestframework-simplejwt==5.3.1",
    "djangorestframework-stubs==3.15.1",
    "drf-spectacular==0.28.0",
    "dropbox>=12.0.2",
    "exceptiongroup==1.2.2",
    "factory-boy==3.3.1",
    "faker==33.3.1",
    "fakeredis>=2.29.0",
    "flower==2.0.1",
    "gunicorn==23.0.0",
    "h11==0.14.0",
    "httpcore==1.0.7",
    "httpx==0.27.2",
    "humanize==4.11.0",
    "idna==3.10",
    "inflection==0.5.1",
    "jsonschema==4.23.0",
    "jsonschema-specifications==2024.10.1",
    "kombu==5.4.2",
    "mypy==1.13.0",
    "mypy-extensions==1.0.0",
    "nudenet>=3.4.2",
    "packaging==24.2",
    "pillow==11.0.0",
    "pillow-heif>=0.21.0",
    "prometheus-client==0.21.1",
    "prompt-toolkit==3.0.48",
    "psycopg2-binary==2.9.10",
    "pyjwt==2.10.1",
    "pytelegrambotapi>=4.26.0",
    "python-crontab==3.2.0",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.1",
    "pytz==2024.2",
    "pyyaml==6.0.2",
    "qrcode==8.0",
    "redis==5.2.1",
    "referencing==0.35.1",
    "requests==2.32.3",
    "rpds-py==0.21.0",
    "ruff==0.8.0",
    "sentry-sdk[celery,django]>=2.20.0",
    "six==1.17.0",
    "sniffio==1.3.1",
    "sqlparse==0.5.2",
    "stripe==11.4.1",
    "tomli==2.1.0",
    "tornado==6.4.2",
    "types-pyyaml==6.0.12.20240917",
    "types-requests==2.32.0.20241016",
    "typing-extensions==4.12.2",
    "tzdata==2024.2",
    "uritemplate==4.1.1",
    "urllib3==2.2.3",
    "validators==0.34.0",
    "vine==5.1.0",
    "wcwidth==0.2.13",
]

[tool.setuptools]
py-modules = []

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "settings",
    "base",
    "manage.py"
]

# Same as Black.
line-length = 88
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`) codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E3", "E4", "E7", "E9", "F", "I"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

preview = true

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"

[tool.mypy]
# The mypy configurations: https://mypy.readthedocs.io/en/latest/config_file.html
python_version = "3.10"
plugins = ["mypy_django_plugin.main"]
exclude = [
    "manage.py",
    "settings",
    "venv"
]

check_untyped_defs = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
ignore_errors = false
ignore_missing_imports = true
implicit_reexport = false
strict_optional = true
strict_equality = true
no_implicit_optional = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
warn_unreachable = true
warn_no_return = true

[tool.django-stubs]
django_settings_module = "core.settings.base"
